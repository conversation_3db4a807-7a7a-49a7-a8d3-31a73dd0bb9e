import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/study/course_rank_model.dart';

class PersonListItem extends StatelessWidget {
  final CourseRankModel item;
  final int index;

  const PersonListItem({
    super.key,
    required this.item,
    required this.index,
  });

  String formatDuration(int duration) {
    double hours = duration / 3600;
    String formattedDuration = hours.toStringAsFixed(2);
    if (formattedDuration.endsWith(".00")) {
      formattedDuration = hours.toStringAsFixed(0);
    }
    return formattedDuration;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        padding: EdgeInsets.symmetric(vertical: 16.h),
        decoration: const BoxDecoration(
            border: Border(
                bottom: BorderSide(color: Color(0xFFE6E6E6), width: 0.5))),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                if (index == 1)
                  Image.asset('assets/png/study/person_first.png',
                      width: 40, height: 40),
                if (index == 2)
                  Image.asset('assets/png/study/person_second.png',
                      width: 40, height: 40),
                if (index == 3)
                  Image.asset('assets/png/study/person_third.png',
                      width: 40, height: 40),
                if (index >= 4)
                  Container(
                    width: 40,
                    height: 40,
                    alignment: Alignment.center,
                    child: Text(
                      '$index',
                      style: TextStyle(
                              color: const Color(0xFF9E9E9E), fontSize: 20.sp)
                          .phBold,
                    ),
                  ),
                SizedBox(width: 12.w),
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl: item.avatar,
                    width: 46,
                    height: 46,
                  ),
                ),
                SizedBox(width: 12.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      (item.userId == GlobalPreferences().userInfo?.user.id)
                          ? "${item.userName}(我)"
                          : item.userName,
                      style: TextStyle(
                              color: const Color(0xFF000000), fontSize: 16.sp)
                          .pfMedium,
                    ),
                    SizedBox(
                      width: 100.w,
                      child: Text(
                        item.departName,
                        style: TextStyle(
                                color: const Color(0xFF464B59),
                                fontSize: 12.sp,
                                overflow: TextOverflow.ellipsis)
                            .pfRegular,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Text(
                      '累计学习',
                      style: TextStyle(
                              color: const Color(0xFF464B59), fontSize: 14.sp)
                          .pfRegular,
                    ),
                    Text(
                      ' ${formatDuration(item.total_duration)} ',
                      style: TextStyle(
                              color: const Color(0xFFFE5D30), fontSize: 16.sp)
                          .phBold,
                    ),
                    Text(
                      '小时',
                      style: TextStyle(
                              color: const Color(0xFF7B7F8A), fontSize: 12.sp)
                          .pfMedium,
                    )
                  ],
                ),
                Row(
                  children: [
                    Text(
                      '今日学习',
                      style: TextStyle(
                          color: const Color(0xFF464B59), fontSize: 14.sp),
                    ),
                    Text(
                      ' ${formatDuration(item.today_duration)} ',
                      style: TextStyle(
                          color: const Color(0xFF3F88F1),
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600),
                    ),
                    Text(
                      '小时',
                      style: TextStyle(
                          color: const Color(0xFF7B7F8A),
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600),
                    )
                  ],
                ),
              ],
            ),
          ],
        ));
  }
}

class PersonListMeItem extends StatelessWidget {
  final CourseRankModel? item;
  final int? sort;
  const PersonListMeItem({super.key, this.item, this.sort});

  String formatDuration(int duration) {
    double hours = duration / 3600;
    String formattedDuration = hours.toStringAsFixed(2);
    if (formattedDuration.endsWith(".00")) {
      formattedDuration = hours.toStringAsFixed(0);
    }
    return formattedDuration;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        color: const Color(0xFFE9F3FD),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl: item?.avatar ??
                        ValidatorUtils.testImageUrl, // item.avatar
                    width: 46,
                    height: 46,
                  ),
                ),
                SizedBox(width: 12.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${item?.userName ?? GlobalPreferences().userInfo?.user.name ?? ''}（我）",
                      style: TextStyle(
                              color: const Color(0xFF000000), fontSize: 16.sp)
                          .pfMedium,
                    ),
                    Row(
                      children: [
                        Container(
                          constraints: BoxConstraints(maxWidth: 80.w),
                          child: Text(
                            item?.departName ??
                                GlobalPreferences().userInfo!.departmentName,
                            style: TextStyle(
                                    color: const Color(0xFF464B59),
                                    fontSize: 12.sp,
                                    overflow: TextOverflow.ellipsis)
                                .pfRegular,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Row(
                          children: [
                            Text(
                              '总排名 ',
                              style: TextStyle(
                                      color: const Color(0xFF464B59),
                                      fontSize: 12.sp)
                                  .pfRegular,
                            ),
                            Text(
                              '${item?.ranking ?? '--'}',
                              style: TextStyle(
                                      color: const Color(0xFF464B59),
                                      fontSize: 13.sp)
                                  .pfSemiBold,
                            )
                          ],
                        )
                      ],
                    ),
                  ],
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Text(
                      '累计学习',
                      style: TextStyle(
                              color: const Color(0xFF464B59), fontSize: 14.sp)
                          .pfRegular,
                    ),
                    Text(
                      ' ${formatDuration(item?.total_duration ?? 0)} ',
                      style: TextStyle(
                              color: const Color(0xFFFE5D30), fontSize: 16.sp)
                          .phBold,
                    ),
                    Text(
                      '小时',
                      style: TextStyle(
                              color: const Color(0xFF7B7F8A), fontSize: 12.sp)
                          .pfMedium,
                    )
                  ],
                ),
                Row(
                  children: [
                    Text(
                      '今日学习',
                      style: TextStyle(
                              color: const Color(0xFF464B59), fontSize: 14.sp)
                          .pfRegular,
                    ),
                    Text(
                      ' ${formatDuration(item?.today_duration ?? 0)} ',
                      style: TextStyle(
                              color: const Color(0xFF3F88F1), fontSize: 16.sp)
                          .phBold,
                    ),
                    Text(
                      '小时',
                      style: TextStyle(
                              color: const Color(0xFF7B7F8A), fontSize: 12.sp)
                          .pfMedium,
                    )
                  ],
                ),
              ],
            ),
          ],
        ));
  }
}
