import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:just_audio/just_audio.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/provider/audio_bloc/audio_event.dart';
import 'package:npemployee/provider/audio_bloc/audio_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';

enum AudioOptions {
  next,
  prev,
  forward,
  backward,
}

class MusicProgress extends StatefulWidget {
  final List<LessonModel> lessons;
  final LessonModel? lesson;
  final int totalTime;
  const MusicProgress(
      {super.key,
      required this.totalTime,
      required this.lessons,
      required this.lesson});

  @override
  State<MusicProgress> createState() => _MusicProgressState();
}

class _MusicProgressState extends State<MusicProgress> {
  double _currentSliderValue = 0; // 当前进度
  int _currentPosition = 0;
  double _totalDuration = 0;

  late AudioPlayer _audioPlayer;
  LessonModel? playingLesson;

  StreamSubscription? _subscription;

  void _audioPlayingListen() {
    _subscription = BlocManager().audioBloc.stream.listen((state) {
      if (state.type == AudioEventType.position) {
        _currentSliderValue = double.parse(state.position.toString());
        _currentPosition = state.position ?? 0;
        if (_currentSliderValue >= _totalDuration) {
          _currentSliderValue = 0;
        }
        setState(() {});
      } else if (state.type == AudioEventType.playingLesson &&
          state.playingLesson != null) {
        // _currentSliderValue = 0;
        playingLesson = state.playingLesson;
        _totalDuration =
            double.parse(state.playingLesson!.total_time.toString());
        setState(() {});
      }
    });
  }

  //提交看课记录
  void _addWatchSave() {
    if (_currentSliderValue > 0) {
      debugPrint('** 上传看课记录:${playingLesson?.id} -- $_currentSliderValue');

      if (playingLesson != null) {
        UserServiceProvider().addCourseWatchSave(playingLesson!.chapter_id,
            playingLesson!.course_id, _currentPosition, playingLesson!.id);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    playingLesson = widget.lesson;
    _totalDuration = double.parse(widget.totalTime.toString());
    _audioPlayer = BlocManager().audioBloc.audioPlayer!;
    _audioPlayingListen();
  }

  @override
  void dispose() {
    _addWatchSave();
    _subscription?.cancel();
    _subscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 进度条
        Container(
          // padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: [
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  trackHeight: 3.h,
                  thumbShape: RoundSliderThumbShape(enabledThumbRadius: 6),
                ),
                child: Slider(
                  value: _currentSliderValue,
                  min: 0,
                  max: _totalDuration,
                  activeColor: Colors.black,
                  inactiveColor: Colors.grey[300],
                  onChanged: (value) {
                    setState(() {
                      _currentSliderValue = value;
                    });
                  },
                  onChangeEnd: (value) {
                    _audioPlayer.seek(Duration(seconds: value.toInt()));
                  },
                ),
              ),
              // 显示当前时间和总时间
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(_currentSliderValue),
                      style: TextStyle(color: Colors.grey, fontSize: 10.sp)
                          .pfSemiBold,
                    ),
                    Text(
                      _formatDuration(_totalDuration),
                      style: TextStyle(color: Colors.grey, fontSize: 10.sp)
                          .pfSemiBold,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 30.h),
        // 控制按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildControlButton('assets/png/xiaoxin/audio_back.png',
                AudioOptions.backward, 20), // 倒退 10 秒
            SizedBox(width: 16),
            _buildControlButton('assets/png/xiaoxin/audio_previous.png',
                AudioOptions.prev, 27), // 上一首
            SizedBox(width: 16),
            _buildPlayPauseButton(), // 播放/暂停
            SizedBox(width: 16),
            _buildControlButton('assets/png/xiaoxin/audio_next.png',
                AudioOptions.next, 27), // 下一首
            SizedBox(width: 16),
            _buildControlButton('assets/png/xiaoxin/audio_forward.png',
                AudioOptions.forward, 20), // 前进 10 秒
          ],
        ),
      ],
    );
  }

  // 格式化时间显示
  String _formatDuration(double seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toStringAsFixed(0).padLeft(2, '0');
    return '$minutes:$secs';
  }

  // 播放/暂停按钮
  Widget _buildPlayPauseButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[800],
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: _audioPlayer.playing
            ? const Icon(Icons.pause, color: Colors.white)
            : const Icon(Icons.play_arrow, color: Colors.white),
        iconSize: 45,
        onPressed: () {
          // 这里可以添加播放/暂停的逻辑
          if (_audioPlayer.playing) {
            _audioPlayer.pause();
          } else {
            _audioPlayer.play();
          }
          setState(() {});
        },
      ),
    );
  }

  // 其他控制按钮（倒退10秒、上一首、下一首、前进10秒）
  Widget _buildControlButton(
      String iconNamed, AudioOptions option, double size) {
    return IconButton(
      icon: ImageIcon(
        AssetImage(
          iconNamed,
        ),
        size: size,
      ),
      iconSize: 30,
      onPressed: () async {
        int index = widget.lessons.indexWhere((e) => e.id == playingLesson?.id);
        if (index == -1) return;
        switch (option) {
          case AudioOptions.next:
            if (index != widget.lessons.length - 1) {
              BlocManager()
                  .audioBloc
                  .add(AudioPlayingEvent(widget.lessons[index + 1]));
            } else {
              EasyLoading.showToast('已经到底了');
            }
            break;
          case AudioOptions.prev:
            if (index != 0) {
              BlocManager()
                  .audioBloc
                  .add(AudioPlayingEvent(widget.lessons[index - 1]));
            } else {
              EasyLoading.showToast('已经到头了');
            }
            break;
          case AudioOptions.forward:
            skipForward();
            break;
          case AudioOptions.backward:
            skipBackward();
            break;
          default:
        }
      },
    );
  }

  void skipForward() {
    final currentPosition = _audioPlayer.position; // 当前播放位置
    final duration = _audioPlayer.duration; // 总时长（可能为 null）

    if (duration != null) {
      // 计算快进后的位置
      final newPosition = currentPosition + const Duration(seconds: 10);
      if (newPosition <= duration) {
        _audioPlayer.seek(newPosition);
      } else {
        _audioPlayer.seek(duration); // 如果超出总时长，则跳到结尾
      }
    }
  }

  void skipBackward() async {
    final currentPosition = _audioPlayer.position; // 当前播放位置

    // 计算倒退后的位置
    final newPosition = currentPosition - const Duration(seconds: 10);
    if (newPosition >= Duration.zero) {
      _audioPlayer.seek(newPosition);
    } else {
      _audioPlayer.seek(Duration.zero); // 如果小于 0，则跳到开头
    }
  }
}
