import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';

import 'department_list_modal.dart';

class EmployeeInfoSection extends StatefulWidget {
  final String userName;
  final String promotionDepartName;
  final String userId;
  final String profileImage;
  final List<DepartmentModel> departments; // List of department models
  final Function(int) onDepartmentSelected; // Callback for department selection

  const EmployeeInfoSection({
    required this.userName,
    required this.userId,
    required this.profileImage,
    required this.departments,
    required this.onDepartmentSelected,
    required this.promotionDepartName,
  });

  @override
  _EmployeeInfoSectionState createState() => _EmployeeInfoSectionState();
}

class _EmployeeInfoSectionState extends State<EmployeeInfoSection> {
  int _selectedDepartmentIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant EmployeeInfoSection oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    if (GlobalPreferences().departmentName != null) {
      int index = widget.departments
          .indexWhere((e) => e.name == GlobalPreferences().departmentName);
      if (index != -1) {
        _selectedDepartmentIndex = index;
        GlobalPreferences().departmentId = widget.departments[index].id;
      }
    }
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Info
          Row(
            children: [
              GestureDetector(
                onDoubleTap: () {
                  Clipboard.setData(ClipboardData(
                      text: jsonEncode(GlobalPreferences().userInfo)));
                  EasyLoading.showToast('账号信息已复制到剪贴板');
                },
                child: CircleAvatar(
                  radius: 30,
                  // borderRadius: BorderRadius.circular(12),
                  backgroundImage: NetworkImage(widget.profileImage.isEmpty
                      ? ValidatorUtils.testImageUrl
                      : widget.profileImage),
                  /* child: Image.network(
                    widget.profileImage.isEmpty
                        ? 'https://avatar.xtjzx.cn/default.png'
                        : widget.profileImage,
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                  ), */
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.userName,
                    style: AppTheme.getTextStyle(
                            baseSize: 18, color: AppTheme.colorPrimaryBlack)
                        .pfSemiBold,
                  ),
                  Text(
                    widget.userId,
                    style: AppTheme.getTextStyle(
                            baseSize: 12, color: AppTheme.colorLightGrey)
                        .pfRegular,
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (_) {
                            return PromotionDepartDialog(
                              departName: widget.promotionDepartName,
                            );
                          });
                    },
                    child: RichText(
                        text: TextSpan(
                      text: '增长数据归属部门: ${widget.promotionDepartName}',
                      style: AppTheme.getTextStyle(
                              baseSize: 12, color: AppTheme.colorLightGrey)
                          .pfRegular,
                      children: [
                        WidgetSpan(child: SizedBox(width: 5.w)),
                        WidgetSpan(
                            child: Image.asset(
                          'assets/png/mine/wenhao.png',
                          width: 15,
                          height: 15,
                        )),
                      ],
                    )),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: const LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Color(0xFFECF6FF), // Left color
                  Color(0xFFF5F7FF), // Right color
                ],
              ),
            ),
            child: widget.departments.length == 1
                ? _buildSingleDepartment(
                    widget.departments.first,
                  )
                : _buildMultiDepartment(),
          ),
        ],
      ),
    );
  }

  // Display for single department case
  Widget _buildSingleDepartment(DepartmentModel department) {
    return Row(
      children: [
        SvgPicture.asset(
          'assets/svg/mine/profile/profile.svg', // Replace with the correct path
          width: 20,
          height: 20,
        ),
        const SizedBox(width: 8),
        Text(
          department.name,
          style: const TextStyle(color: Colors.black, fontSize: 13)
              .pfMedium, // Black color for single department
        ),
        const SizedBox(width: 8),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: department.positions
                  .map((position) => Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 4, horizontal: 8),
                        margin: const EdgeInsets.only(right: 6),
                        decoration: BoxDecoration(
                          color: Color(0xFF3377FF),
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: Text(
                          position,
                          style:
                              const TextStyle(color: Colors.white, fontSize: 10)
                                  .pfRegular,
                        ),
                      ))
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }

// Display for multi-department case
  Widget _buildMultiDepartment() {
    return Row(
      children: [
        SvgPicture.asset(
          'assets/svg/mine/profile/profile.svg', // Replace with the correct path
          width: 20,
          height: 20,
        ),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: () {
            // Get the position of the tap
            RenderBox renderBox = context.findRenderObject() as RenderBox;
            Offset position = renderBox.localToGlobal(Offset.zero);

            showGeneralDialog(
              context: context,
              barrierDismissible: true, // Dismiss when tapping outside
              barrierColor: Colors.transparent, // Transparent background
              barrierLabel: 'Department Selection', // Add this line
              pageBuilder: (context, animation, secondaryAnimation) {
                return DepartmentListModal(
                  departments: widget.departments,
                  selectedDepartmentIndex: _selectedDepartmentIndex,
                  onDepartmentSelected: (index) {
                    setState(() {
                      _selectedDepartmentIndex = index;
                    });
                    GlobalPreferences().departmentName =
                        widget.departments[_selectedDepartmentIndex].name;
                    widget.onDepartmentSelected(index);
                  },
                  position:
                      Offset(position.dx, position.dy + renderBox.size.height),
                );
              },
              transitionBuilder:
                  (context, animation, secondaryAnimation, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
            );
          },
          child: Row(
            children: [
              Text(
                widget.departments[_selectedDepartmentIndex].name,
                style: const TextStyle(
                    color: AppTheme.colorBlue, fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 4),
              Text(
                '等${widget.departments.length}个部门',
                style: AppTheme.getTextStyle(
                  baseSize: 13,
                  color: AppTheme.colorPrimaryBlack,
                ),
              ),
              const SizedBox(width: 4),
              SvgPicture.asset(
                'assets/svg/mine/profile/right_arrow.svg',
                width: 7.47, // Adjust size as needed
                height: 13.58,
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        // Positions of the selected department remain on the same line
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: widget.departments[_selectedDepartmentIndex].positions
                  .map((position) => Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 4, horizontal: 8),
                        margin: const EdgeInsets.only(right: 6),
                        decoration: BoxDecoration(
                          color: Color(0xFF3377FF),
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: Text(
                          position,
                          style:
                              const TextStyle(color: Colors.white, fontSize: 10)
                                  .pfRegular,
                        ),
                      ))
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }
}

// Model class for the department and positions
class DepartmentModel {
  final String name;
  final List<String> positions;
  final int id;

  DepartmentModel({
    required this.id,
    required this.name,
    required this.positions,
  });
}

class PromotionDepartDialog extends StatelessWidget {
  final String departName;
  const PromotionDepartDialog({super.key, required this.departName});

  bool get isBind => departName != '未绑定';

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        child: Dialog(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 28.w, vertical: 22.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              gradient: const LinearGradient(
                  colors: [Color(0xFFE5EDFF), Color(0xFFFFFFFF)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '提示',
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 17.sp)
                          .pfSemiBold,
                ),
                SizedBox(height: 19.h),
                if (isBind)
                  Text(
                    '由于您所在多个部门',
                    style: TextStyle(
                            color: const Color(0xFF333333),
                            fontSize: 15.sp,
                            height: 25.sp / 15.sp)
                        .pfRegular,
                  ),
                if (isBind)
                  RichText(
                      text: TextSpan(
                    text: '当前增长数据统计到',
                    style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 15.sp,
                    ).pfRegular,
                    children: [
                      TextSpan(
                        text: '$departName,',
                        style: TextStyle(
                                color: AppTheme.colorBlue,
                                fontSize: 15.sp,
                                height: 25.sp / 15.sp)
                            .pfRegular,
                      )
                    ],
                  )),
                if (isBind)
                  RichText(
                      text: TextSpan(
                    text: '如需更换请联系',
                    style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 15.sp,
                    ).pfRegular,
                    children: [
                      WidgetSpan(
                          child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          WeChatService().launchWeChatWork();
                        },
                        child: Text(
                          '在线客服。',
                          style: TextStyle(
                                  color: AppTheme.colorBlue,
                                  fontSize: 15.sp,
                                  decoration: TextDecoration.underline,
                                  decorationColor: AppTheme.colorBlue,
                                  height: 25.sp / 15.sp)
                              .pfRegular,
                        ),
                      )),
                    ],
                  )),
                if (!isBind)
                  RichText(
                      text: TextSpan(
                    text: '您',
                    style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 15.sp,
                    ).pfRegular,
                    children: [
                      TextSpan(
                        text: '暂未绑定',
                        style: TextStyle(
                                color: AppTheme.colorBlue,
                                fontSize: 15.sp,
                                height: 25.sp / 15.sp)
                            .pfRegular,
                      ),
                      TextSpan(
                        text: '增长数据归属部门，如有问题请联系',
                        style: TextStyle(
                                color: const Color(0xFF333333),
                                fontSize: 15.sp,
                                height: 25.sp / 15.sp)
                            .pfRegular,
                      ),
                      WidgetSpan(
                          child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          WeChatService().launchWeChatWork();
                        },
                        child: Text(
                          '在线客服。',
                          style: TextStyle(
                                  color: AppTheme.colorBlue,
                                  fontSize: 15.sp,
                                  decoration: TextDecoration.underline,
                                  decorationColor: AppTheme.colorBlue,
                                  height: 25.sp / 15.sp)
                              .pfRegular,
                        ),
                      )),
                    ],
                  )),
                SizedBox(height: 25.h),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    NavigatorUtils.pop(context);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    decoration: BoxDecoration(
                      color: AppTheme.colorBlue,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      '确认',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 15.sp,
                      ).pfMedium,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
