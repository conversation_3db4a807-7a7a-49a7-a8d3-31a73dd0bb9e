import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/input_dialog.dart';
import 'package:npemployee/page/home/<USER>';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class ApprovalCell extends StatelessWidget {
  final String avatar;
  final String name;
  final List role;
  final String phone;
  final String idCard;
  final String time;
  final bool showButtons; // Determines if buttons should be shown
  final String? remarks; // Optional remarks section
  final int? id;
  final VoidCallback? callback; //审核成功刷新页面

  const ApprovalCell({
    Key? key,
    required this.avatar,
    required this.name,
    required this.role,
    required this.phone,
    required this.idCard,
    required this.time,
    this.showButtons = false, // By default, no buttons
    this.remarks, // Remarks are optional
    this.id,
    this.callback,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(9),
                child: Image.network(
                  avatar.isEmpty ? ValidatorUtils.testImageUrl : avatar,
                  width: 45,
                  height: 45,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: AppTheme.getTextStyle(
                      baseSize: 15.sp,
                      color: const Color(0xFF323640),
                    ).pfSemiBold,
                  ),
                  SizedBox(height: 3.h),
                  ...role.map(
                    (e) => Text(
                      e,
                      style: AppTheme.getTextStyle(
                        baseSize: 12.sp,
                        color: const Color(0xFF8B90A0),
                      ).pfRegular,
                    ),
                  )
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                '手机号码',
                style: AppTheme.getTextStyle(
                  baseSize: 14.sp,
                  color: const Color(0xFF999999),
                ).pfRegular,
              ),
              const SizedBox(width: 20),
              Text(
                phone,
                style: AppTheme.getTextStyle(
                  baseSize: 14.sp,
                  color: const Color(0xFF333333),
                ).pfRegular,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                '身份证号',
                style: AppTheme.getTextStyle(
                  baseSize: 14.sp,
                  color: const Color(0xFF999999),
                ).pfRegular,
              ),
              const SizedBox(width: 20),
              Text(
                idCard,
                style: AppTheme.getTextStyle(
                  baseSize: 14.sp,
                  color: const Color(0xFF333333),
                ).pfRegular,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                '申请时间',
                style: AppTheme.getTextStyle(
                  baseSize: 14.sp,
                  color: const Color(0xFF999999),
                ).pfRegular,
              ),
              const SizedBox(width: 20),
              Text(
                time,
                style: AppTheme.getTextStyle(
                  baseSize: 14,
                  color: const Color(0xFF333333),
                ).pfRegular,
              ),
            ],
          ),
          if (remarks != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  '备注　　',
                  style: AppTheme.getTextStyle(
                    baseSize: 14.sp,
                    color: const Color(0xFF999999),
                  ).pfRegular,
                ),
                const SizedBox(width: 20),
                Expanded(
                    child: Text(
                  remarks!,
                  style: AppTheme.getTextStyle(
                    baseSize: 14.sp,
                    color: const Color(0xFF333333),
                  ).pfRegular,
                )),
              ],
            ),
          ],
          if (showButtons) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: () {
                    _showInputDialog(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFECECEC),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    elevation: 0, // No shadow
                  ),
                  child: Text(
                    '驳回',
                    style: AppTheme.getTextStyle(
                      baseSize: 13.sp,
                      color: const Color(0xFFB3B3B3),
                    ).pfMedium,
                  ),
                ),
                const SizedBox(width: 10),
                ElevatedButton(
                  onPressed: () {
                    UserServiceProvider()
                        .approvalWithType(id!, "REVIEW_PASS")
                        .then((value) {
                      if (value?.code == 0) {
                        callback!();
                      } else {
                        EasyLoading.showToast('${value?.msg}');
                      }
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0054FF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    elevation: 0, // No shadow
                  ),
                  child: Text(
                    '通过',
                    style: AppTheme.getTextStyle(
                      baseSize: 13.sp,
                      color: Colors.white,
                    ).pfMedium,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _showInputDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => InputDialog(
        title: "驳回理由",
        placeholder: "请输入驳回理由",
        onCancel: () {
          Navigator.of(context).pop();
        },
        onConfirm: (inputText) {
          // Handle the inputText here
          print("Entered text: $inputText");
          UserServiceProvider()
              .approvalWithType(id!, "REVIEW_REJECT", note: inputText)
              .then((value) {
            if (value?.code == 0) {
              callback!();
              NavigatorUtils.pop(context);
            } else {
              EasyLoading.showToast('${value?.msg}');
            }
          });
          // Further processing like saving the reason or performing an action
        },
      ),
    );
  }
}
