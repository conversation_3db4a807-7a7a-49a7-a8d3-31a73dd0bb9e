import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/im/contacts_page.dart';
import 'package:npemployee/page/im/group/add_group.dart';
import 'package:npemployee/page/im/message_detail_page.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_conversation.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class MessageListPage extends StatefulWidget {
  const MessageListPage({super.key});

  @override
  State<MessageListPage> createState() => _MessageListPageState();
}

class _MessageListPageState extends State<MessageListPage> {
  final List<ContactModel> _contacts = [];

  void _getContacts() {
    UserServiceProvider().getContacts(
        cacheCallBack: (data) {
          _formatContactsData(data, isCache: true);
        },
        successCallBack: (data) {
          _formatContactsData(data);
        },
        errorCallBack: (err) {});
  }

  void _formatContactsData(ResultData? data, {bool isCache = false}) {
    _contacts.clear();
    for (var element in data?.data ?? []) {
      if (element['user_count'] != 0) {
        _contacts.add(ContactModel.fromJson(element));
      }
    }
    if (mounted) {
      setState(() {});
    }
  }

  void _subscriptionTabChangeBloc() {
    BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change && state.page == 2) {
        _getContacts();
        debugPrint("-- 进入消息页面 ${state.page}");
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _getContacts();

    ///监听页面切换
    _subscriptionTabChangeBloc();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        body: Column(
          children: [
            Container(
              color: Colors.white,
              padding: EdgeInsets.only(left: 16.w),
              height: 88.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '消息',
                    style: TextStyle(
                            fontSize: 22.sp, color: AppTheme.colorBlackTitle)
                        .pfSemiBold,
                  ),
                  Row(
                    children: [
                      _topRightBtn('group_add.png', () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const ImAddGroup()));
                      }),
                      _topRightBtn('contact_icon.png', () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ContactsPage()));
                      }),
                      SizedBox(width: 6.w),
                    ],
                  ),
                ],
              ),
            ),
            _searchView(),
            // SizedBox(height: 16.h),
            Expanded(
              child: ITimUikitConversation(
                contacts: _contacts,
                onTapItem: (selectedConv) {
                  NavigatorUtils.push(context, MessageRouter.messageDetailPage,
                      arguments: {'con': selectedConv});
                },
                emptyBuilder: () {
                  return Container(
                    width: ScreenUtil().screenWidth,
                    height: ScreenUtil().screenHeight - 88.h - 81.h - 56,
                    child: Column(
                      children: [
                        SizedBox(height: 100.h),
                        Image.asset('assets/png/im/im_no_data.png',
                            width: 130.w, height: 114.h),
                        SizedBox(height: 14.h),
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: '还没有消息，请',
                                style: TextStyle(
                                        color: const Color(0xFFB0B5BF),
                                        fontSize: 14.sp)
                                    .pfRegular,
                              ),
                              TextSpan(
                                text: '开始聊天',
                                style: TextStyle(
                                  color: const Color(0xFF0054FF),
                                  fontSize: 14.sp,
                                  decoration: TextDecoration.underline,
                                ).pfRegular,
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                ContactsPage()));
                                  },
                              ),
                              TextSpan(
                                text: '吧～',
                                style: TextStyle(
                                        color: const Color(0xFFB0B5BF),
                                        fontSize: 14.sp)
                                    .pfRegular,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ));
  }

  Widget _topRightBtn(String imgPath, Function() onTap) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
        child: Image.asset('assets/png/im/$imgPath', width: 25.w, height: 25.h),
      ),
    );
  }

  Widget _searchView() {
    return Container(
      alignment: Alignment.center,
      color: Colors.white,
      width: ScreenUtil().screenWidth,
      height: 81.h,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          NavigatorUtils.push(context, MessageRouter.messageSearchPage,
              arguments: {'contacts': _contacts});
        },
        child: SizedBox(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            width: 343.w,
            height: 44.h,
            decoration: BoxDecoration(
              color: '#F7F8FD'.toColor(),
              borderRadius: BorderRadius.circular(5.5.r),
            ),
            child: Row(
              children: [
                Icon(Icons.search, color: '#BABABA'.toColor(), size: 25),
                SizedBox(width: 10.w),
                Text('搜索',
                    style: TextStyle(color: 'BABABA'.toColor(), fontSize: 15.sp)
                        .pfRegular)
              ],
            ),
          ),
        ),
      ),
    );
  }
}
