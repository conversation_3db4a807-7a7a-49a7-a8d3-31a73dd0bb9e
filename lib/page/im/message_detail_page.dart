import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marqueer/marqueer.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/im_utils.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/page/im/calling_message/calling_message_data_provider.dart';
import 'package:npemployee/page/im/custom_message_element.dart';
import 'package:npemployee/page/im/group/profile.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_chat.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/chat_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';

class MessageDetailPage extends StatefulWidget {
  final V2TimConversation con;
  final V2TimMessage? findingMsg;
  const MessageDetailPage({super.key, required this.con, this.findingMsg});

  @override
  State<MessageDetailPage> createState() => _MessageDetailPageState();
}

class _MessageDetailPageState extends State<MessageDetailPage> {
  final TIMUIKitChatController _chatController = TIMUIKitChatController();

  bool isSystem = false; //判断是否显示输入框 系统消息类不显示

  String conName = '';
  String? groupNotification;

  String get departName {
    String departmentNames = '';
    Map departInfo = widget.con.contactUserInfo ?? {};
    if (departInfo.isNotEmpty && departInfo['list'] != null) {
      List<Map<String, String>> list =
          List<Map<String, String>>.from(departInfo['list']);
      departmentNames = list.map((item) => item['department']).join(' | ');
    }
    return departmentNames;
  }

  Future<bool> _checkGroupMembership(String groupID) async {
    final memberId = GlobalPreferences().userInfo!.user.id.toString();
    final res = await TencentImSDKPlugin.v2TIMManager
        .getGroupManager()
        .getGroupMembersInfo(groupID: groupID, memberList: [memberId]);

    if (res.code == 0) {
      return res.data?.isNotEmpty ?? false;
    } else if (res.code == 10007) {
      // 10007 表示用户不在群组中
      return false;
    } else {
      print("获取群成员信息失败: ${res.code} - ${res.desc}");
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    isSystem = getIsSystem() == null;
    conName = widget.con.showName ?? '';
    if (widget.con.type == 2) {
      _getGroupInfo();
    }
  }

  int? getIsSystem() {
    if (widget.con.type == 2) {
      //type=2 群聊不是系统消息，显示输入框
      return 999999;
    }
    try {
      int? status = int.tryParse(widget.con.userID!);
      return status;
    } catch (e) {
      return null;
    }
  }

  void _getGroupInfo() {
    TencentImSDKPlugin.v2TIMManager
        .getGroupManager()
        .getGroupsInfo(groupIDList: [widget.con.groupID!]).then((value) {
      if (value.code == 0) {
        setState(() {
          groupNotification = value.data?.first.groupInfo?.notification;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop) {
            NavigatorUtils.popUntil(context, CommonRouter.tabs);
          }
        },
        child: ITimUikitChat(
          appBarConfig: _appBarBuilder(),
          groupNotification: groupNotification,
          groupID: widget.con.groupID,
          initFindingMsg: widget.findingMsg,
          onUpdateGroupNotification: () {
            _getGroupInfo();
          },
          onTapAvatar: (userID, tapDetails) {
            if (userID == GlobalPreferences().userInfo?.user.id.toString()) {
              return;
            }
            if (widget.con.type == 2) {
              NavigatorUtils.push(context, MessageRouter.userProfilePage3,
                  arguments: {'userId': userID});
            } else {
              NavigatorUtils.push(context, MessageRouter.userProfilePage2,
                  arguments: {'con': widget.con});
            }
          },
          // onClickBack: () {
          //   BlocManager().tabBloc.add(TabChangeEvent(2));
          //   Navigator.popUntil(context, ModalRoute.withName(CommonRouter.tabs));
          // },
          textFieldBuilder: !isSystem
              ? null
              : (context) {
                  return Container();
                },
          config: TIMUIKitChatConfig(
            isShowReadingStatus: false,
            isAllowEmojiPanel: false,
          ),
          lifeCycle: ChatLifeCycle(messageListShouldMount: (messageList) {
            List<V2TimMessage> list = [];
            for (V2TimMessage message in messageList) {
              CallingMessageDataProvider provide =
                  CallingMessageDataProvider(message);
              if (!provide.isCallingSignal || !provide.excludeFromHistory) {
                list.add(message);
              }
            }
            return list;
          }),

          messageItemBuilder: MessageItemBuilder(
            /* textMessageItemBuilder: (message, isShowJump, clearJump) {
          return TextMessageElement(
            message: message,
            isFromSelf: message.isSelf ?? true,
            isShowJump: isShowJump,
            clearJump: clearJump,
          );
        }, */
            customMessageItemBuilder: (message, isShowJump, clearJump) {
              return CustomMessageElem(
                message: message,
                isShowJump: isShowJump,
                clearJump: clearJump,
                chatController: _chatController,
              );
            },
          ),
          textFieldHintText: '请输入',
          conversation: widget.con,
        ));
  }

  AppBar _appBarBuilder() {
    return AppBar(
      title: Column(
        children: [
          Text(
            conName,
            style:
                TextStyle(color: Color(0xFF000000), fontSize: 15.sp).pfSemiBold,
          ),
          if (widget.con.type == 1)
            LayoutBuilder(builder: (_, constraints) {
              final textPainter = TextPainter(
                text: TextSpan(
                  text: departName,
                  style: TextStyle(color: Color(0xFF999999), fontSize: 10.sp)
                      .pfMedium,
                ),
                maxLines: 1,
                textDirection: TextDirection.ltr,
              )..layout(maxWidth: constraints.maxWidth);
              final isOverflowing = textPainter.didExceedMaxLines;
              final textHeight = textPainter.size.height;

              return isOverflowing
                  ? SizedBox(
                      height: textHeight,
                      child: Marqueer(
                        interaction: false,
                        separatorBuilder: (context, index) =>
                            const SizedBox(width: 8),
                        child: Text(
                          departName,
                          textAlign: TextAlign.left,
                          maxLines: 1,
                          style: TextStyle(
                                  color: Color(0xFF999999), fontSize: 10.sp)
                              .pfMedium,
                        ),
                      ),
                    )
                  : Text(
                      departName,
                      textAlign: TextAlign.left,
                      maxLines: 1,
                      style:
                          TextStyle(color: Color(0xFF999999), fontSize: 10.sp)
                              .pfMedium,
                    );
            }),
        ],
      ),
      leading: IconButton(
        onPressed: () {
          NavigatorUtils.popUntil(context, CommonRouter.tabs);
        },
        icon: const Icon(
          Icons.arrow_back_ios,
          color: Color(0xFF000000),
        ),
        iconSize: 18,
      ),
      actions: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            final navigatorContext = Navigator.of(context);
            if (widget.con.type == 1) {
              NavigatorUtils.push(context, MessageRouter.complaintPage);
            } else {
              final groupID = widget.con.groupID;
              if (groupID != null) {
                _checkGroupMembership(groupID).then((res) {
                  if (!res) {
                    EasyLoading.showToast('您未在该群组中，请联系管理员');
                    return;
                  }
                  navigatorContext
                      .push(MaterialPageRoute(
                    builder: (context) => GroupProfile(
                      updateGroupName: (updateGroupName) {
                        setState(() {
                          conName = updateGroupName;
                        });
                      },
                      groupID: groupID,
                    ),
                  ))
                      .then((v) {
                    _getGroupInfo();
                  });
                });
              }
            }
          },
          child: Container(
            margin: EdgeInsets.only(right: 16.w),
            alignment: Alignment.center,
            width: 24,
            height: 24,
            child: Image.asset('assets/png/im/more.png'),
          ),
        )
      ],
    );
  }
}
