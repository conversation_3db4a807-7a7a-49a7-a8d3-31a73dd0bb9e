import 'dart:io';
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart' as tui;
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/text_input_bottom_sheet.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

Future<AssetEntity?> _pickFromCamera(BuildContext c) {
  return CameraPicker.pickFromCamera(
    c,
    pickerConfig: const CameraPickerConfig(enableRecording: true),
  );
}

class ITimUikitGroupProfileDetailCard extends TIMUIKitStatelessWidget {
  final V2TimGroupInfo groupInfo;
  final void Function(String groupName)? updateGroupName;
  final TextEditingController controller = TextEditingController();
  final bool isHavePermission;

  ITimUikitGroupProfileDetailCard(
      {super.key,
      required this.groupInfo,
      this.isHavePermission = false,
      this.updateGroupName});

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    final model = Provider.of<TUIGroupProfileModel>(context);
    final faceUrl = groupInfo.faceUrl ?? "";
    final groupID = groupInfo.groupID;
    final showName = groupInfo.groupName ?? groupID;
    final memberList = model.groupMemberList;
    final ownerMember = memberList.isNotEmpty
        ? memberList.firstWhere((e) => e?.userID == groupInfo.owner,
            orElse: () => null)
        : null;
    final nameCard = ownerMember?.nameCard ?? '';
    final nickName = ownerMember?.nickName ?? '';
    final ownerName = nameCard.isNotEmpty ? nameCard : nickName;
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;

    return InkWell(
      onTapDown: !isHavePermission
          ? null
          : ((details) {
              if (isDesktopScreen) {
                TextInputBottomSheet.showTextInputBottomSheet(
                    context: context,
                    title: TIM_t("修改群名称"),
                    initText: showName,
                    initOffset: Offset(
                        min(details.globalPosition.dx,
                            MediaQuery.of(context).size.width - 350),
                        min(details.globalPosition.dy + 20,
                            MediaQuery.of(context).size.height - 470)),
                    onSubmitted: (String newText) async {
                      final text = newText.trim();
                      if (updateGroupName != null) {
                        updateGroupName!(text);
                      } else {
                        model.setGroupName(text);
                      }
                    },
                    theme: theme);
              } else {
                /*  */
                showCupertinoModalPopup<String>(
                  context: context,
                  builder: (BuildContext context) {
                    return CupertinoActionSheet(
                        cancelButton: CupertinoActionSheetAction(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          isDefaultAction: false,
                          child: Text(
                            TIM_t("取消"),
                            style: TextStyle(
                                    color: 'FA4141'.toColor(), fontSize: 16.sp)
                                .pfMedium,
                          ),
                        ),
                        actions: [
                          CupertinoActionSheetAction(
                            onPressed: () {
                              Navigator.pop(context);
                              controller.text = groupInfo.groupName ?? "";
                              showModalBottomSheet(
                                  isScrollControlled: true,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(24.r),
                                  ),
                                  context: context,
                                  builder: (context) {
                                    return Container(
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(24.r),
                                              topRight: Radius.circular(24.r))),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 20),
                                            child: Text(
                                              TIM_t("修改群名称"),
                                              style: TextStyle(
                                                      color: '333333'.toColor(),
                                                      fontSize: 16.sp)
                                                  .pfMedium,
                                            ),
                                          ),
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 16.w),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                TextField(
                                                  controller: controller,
                                                  textAlignVertical:
                                                      TextAlignVertical.center,
                                                  textAlign: TextAlign.left,
                                                  style: TextStyle(
                                                          color: '333333'
                                                              .toColor(),
                                                          fontSize: 15.sp)
                                                      .pfMedium,
                                                  decoration: InputDecoration(
                                                      fillColor:
                                                          'ffffff'.toColor(),
                                                      filled: true,
                                                      isDense: true,
                                                      border:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12.r),
                                                        borderSide: BorderSide(
                                                          color: '979797'
                                                              .toColor()
                                                              .withOpacity(0.1),
                                                          width: 0.5,
                                                        ),
                                                      ),
                                                      enabledBorder:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12.r),
                                                        borderSide: BorderSide(
                                                          color: '979797'
                                                              .toColor()
                                                              .withOpacity(0.5),
                                                          width: 0.5,
                                                        ),
                                                      ),
                                                      focusedBorder:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12.r),
                                                        borderSide: BorderSide(
                                                          color: '979797'
                                                              .toColor()
                                                              .withOpacity(0.5),
                                                          width: 0.5,
                                                        ),
                                                      ),
                                                      hintText: '请输入群聊名称',
                                                      hintStyle: TextStyle(
                                                              color: '777777'
                                                                  .toColor(),
                                                              fontSize: 15.sp)
                                                          .pfRegular),
                                                ),
                                                SizedBox(height: 42.h),
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: SizedBox(
                                                          child: ElevatedButton(
                                                        style: ButtonStyle(
                                                            padding: WidgetStatePropertyAll(
                                                                EdgeInsets.symmetric(
                                                                    vertical:
                                                                        14.5
                                                                            .h)),
                                                            backgroundColor:
                                                                WidgetStatePropertyAll(
                                                                    'ECECEC'
                                                                        .toColor()),
                                                            elevation:
                                                                WidgetStatePropertyAll(
                                                                    0),
                                                            shape: WidgetStatePropertyAll(
                                                                RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            16.r)))),
                                                        onPressed: () {
                                                          Navigator.pop(
                                                              context);
                                                        },
                                                        child: Text(TIM_t("取消"),
                                                            style: TextStyle(
                                                                    color: 'B3B3B3'
                                                                        .toColor())
                                                                .pfMedium),
                                                      )),
                                                    ),
                                                    SizedBox(width: 16.w),
                                                    Expanded(
                                                      child: SizedBox(
                                                          child: ElevatedButton(
                                                        style: ButtonStyle(
                                                            padding: WidgetStatePropertyAll(
                                                                EdgeInsets.symmetric(
                                                                    vertical:
                                                                        14.5
                                                                            .h)),
                                                            backgroundColor:
                                                                WidgetStatePropertyAll(
                                                                    '0054FF'
                                                                        .toColor()),
                                                            elevation:
                                                                const WidgetStatePropertyAll(
                                                                    0),
                                                            shape: WidgetStatePropertyAll(
                                                                RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            16.r)))),
                                                        onPressed: () {
                                                          final text =
                                                              controller.text
                                                                  .trim();
                                                          if (updateGroupName !=
                                                              null) {
                                                            updateGroupName!(
                                                                text);
                                                          }
                                                          model.setGroupName(
                                                              text);
                                                          NavigatorUtils.pop(
                                                              context);
                                                        },
                                                        child: Text(TIM_t("确定"),
                                                            style: TextStyle(
                                                                    color: 'ffffff'
                                                                        .toColor())
                                                                .pfMedium),
                                                      )),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(
                                                  height: 20,
                                                ),
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                      bottom:
                                                          MediaQuery.of(context)
                                                              .viewInsets
                                                              .bottom),
                                                )
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    );
                                  });
                            },
                            isDefaultAction: false,
                            child: Text(TIM_t("修改群名称"),
                                style: TextStyle(
                                        color: theme.primaryColor,
                                        fontSize: 16.sp)
                                    .pfMedium),
                          ),
                          CupertinoActionSheetAction(
                            onPressed: () {
                              Navigator.pop(context);
                              _changeAvatar(context);
                            },
                            child: Text(TIM_t("修改群头像"),
                                style: TextStyle(
                                        color: theme.primaryColor,
                                        fontSize: 16.sp)
                                    .pfMedium),
                          )
                        ]);
                  },
                );
              }
            }),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 8.5.w),
        padding: EdgeInsets.only(
            top: isDesktopScreen ? 20 : 16.h,
            bottom: isDesktopScreen ? 20 : 16.h,
            right: isDesktopScreen ? 16 : 16.w,
            left: 16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: isDesktopScreen ? 40 : 40,
              height: isDesktopScreen ? 40 : 40,
              child: Avatar(
                faceUrl: faceUrl,
                showName: showName,
                type: 2,
              ),
            ),
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(left: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SelectableText(
                      '$showName (${memberList.length})',
                      style: TextStyle(
                              fontSize: isDesktopScreen ? 15 : 15.sp,
                              color: '000000'.toColor(),
                              height: 24.sp / 15.sp)
                          .pfMedium,
                    ),
                    SelectableText("群主: $ownerName",
                        style: TextStyle(
                                fontSize: isDesktopScreen ? 13 : 12.sp,
                                color: '81848B'.toColor())
                            .pfRegular)
                  ],
                ),
              ),
            ),
            if (isHavePermission)
              Image.asset('assets/png/arrow_right.png', width: 10.w),
          ],
        ),
      ),
    );
  }

  void _changeAvatar(BuildContext cxt) async {
    final navigatorContext = Navigator.of(cxt);
    List<AssetEntity>? _assets = [];
    _assets = await AssetPicker.pickAssets(
      cxt,
      pickerConfig: AssetPickerConfig(
        maxAssets: 1,
        selectedAssets: _assets,
        specialItemPosition: SpecialItemPosition.prepend,
        requestType: RequestType.image,
        pathNameBuilder: (AssetPathEntity path) => switch (path) {
          final p when p.isAll => '全部',
          _ => path.name,
        },
        specialItemBuilder: (
          BuildContext _,
          AssetPathEntity? path,
          int length,
        ) {
          if (path?.isAll != true) {
            return null;
          }
          return Semantics(
            // label: textDelegate.sActionUseCameraHint,
            button: true,
            // onTapHint: textDelegate.sActionUseCameraHint,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () async {
                Feedback.forTap(_);
                final AssetEntity? result = await _pickFromCamera(_);
                if (result != null) {
                  _assets = [result];
                  _setAvatar(result, navigatorContext);
                }
              },
              child: Container(
                padding: const EdgeInsets.all(28.0),
                color: Theme.of(_).dividerColor,
                child: const FittedBox(
                  fit: BoxFit.fill,
                  child: Icon(Icons.camera_enhance),
                ),
              ),
            ),
          );
        },
      ),
    );
    if (_assets != null && _assets!.isNotEmpty) {
      AssetEntity element = _assets!.first;
      _setAvatar(element, navigatorContext);
    }
  }

  void _setAvatar(AssetEntity element, NavigatorState cxt) async {
    File? file = await element.file;
    //裁剪
    final croppedFile =
        await ImageCropper().cropImage(sourcePath: file!.path, uiSettings: [
      AndroidUiSettings(
        toolbarTitle: '编辑头像',
        toolbarColor: Colors.black,
        toolbarWidgetColor: Colors.white,
      ),
      IOSUiSettings(
        title: '编辑头像',
        aspectRatioLockEnabled: true,
      ),
    ]);
    if (croppedFile != null) {
      EasyLoading.show();
      ResultData? res =
          await UserServiceProvider().uploadAvatar(croppedFile.path);
      if (res?.code != 0) {
        EasyLoading.dismiss();
        EasyLoading.showToast('头像上传失败，请重试');
        return;
      }
      final infoRes = await TIMUIKitCore.getSDKInstance()
          .getGroupManager()
          .setGroupInfo(
              info: V2TimGroupInfo(
                  groupID: groupInfo.groupID,
                  groupType: GroupType.Work,
                  faceUrl: res?.data));
      EasyLoading.dismiss();
      if (infoRes.code != 0) {
        EasyLoading.showToast('头像设置失败，请重试');
        return;
      }
      cxt.pop();
    }
  }
}
