// Page for 健康榜
import 'dart:async';
import 'dart:io';

import 'package:daily_pedometer/daily_pedometer.dart';
import 'package:easy_refresh/easy_refresh.dart';
// import 'package:daily_pedometer2/daily_pedometer2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/custom_dialog.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/methodchannel/health_kit_channel.dart';
import 'package:npemployee/model/study/health_pk_model.dart';
import 'package:npemployee/model/study/health_rank_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/study/health_pk_item.dart';
import 'package:npemployee/widget/study/health_step_item.dart';
import 'package:health/health.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

class HealthRankPage extends StatefulWidget {
  const HealthRankPage({super.key});

  @override
  State<HealthRankPage> createState() => _HealthRankPageState();
}

class _HealthRankPageState extends State<HealthRankPage> {
  int topTabIndex = 1; //1-步数榜 2-健康PK榜

  List<HealthPkModel> healthPkList = [];

  StreamSubscription? subscription;

  List<HealthRankModel> healthRankList = [];
  HealthRankModel? rankMe;

  bool? stepPermission;
  int stepCount = 0;

  Health health = Health();
  final types = [HealthDataType.STEPS];
  final permissions = [HealthDataAccess.READ_WRITE];

  // late Stream<StepCount> _dailyStepCountStream;
  // late Stream<StepCount> _stepCountStream;

  final EasyRefreshController _refreshController = EasyRefreshController();

  void _getPKData() {
    UserServiceProvider().getHealthPKList(DateTime.now().year,
        cacheCallBack: (value) {
      _formatPKData(value, true);
    }, successCallBack: (value) {
      _formatPKData(value, false);
    }, errorCallBack: (error) {
      EasyLoading.showError(error.msg);
    });
  }

  void _getRankData() async {
    UserServiceProvider().getHealthWalkRankList(cacheCallBack: (value) {
      _formatRankData(value, true);
    }, successCallBack: (value) {
      _formatRankData(value, false);
    }, errorCallBack: (error) {
      EasyLoading.showError(error.msg);
    });
  }

  void _addStepCount() {
    UserServiceProvider().addHealthStep(stepCount).then((value) {
      if (value?.code == 0) {
        _getRankData();
      }
    });
  }

  void _formatPKData(ResultData value, bool isCache) {
    healthPkList.clear();
    for (var element in value.data) {
      healthPkList.add(HealthPkModel.formJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _formatRankData(ResultData value, bool isCache) {
    healthRankList.clear();
    for (var element in value.data) {
      healthRankList.add(HealthRankModel.formJson(element));
      if (element['user']['name'] == GlobalPreferences().userInfo?.user.name) {
        rankMe = HealthRankModel.formJson(element);
      }
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _subscriptionTabChangeBloc() {
    subscription = BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change && state.page == 97) {
        print("-- 进入健康榜页面 ${state.page}");
        topTabIndex == 1 ? _getRankData() : _getPKData();
      }
    });
  }

  void _tabChange(int i) {
    setState(() {
      topTabIndex = i;
    });
    i == 1 ? _getRankData() : _getPKData();
    if (i == 1) {
      _getTodayStep();
    }
  }

  Future<void> _getTodayStep() async {
    if (Platform.isAndroid) {
      try {
        if (await Permission.activityRecognition.status ==
            PermissionStatus.granted) {
          stepPermission = true;
          /* _dailyStepCountStream = DailyPedometer2.dailyStepCountStream;
          _dailyStepCountStream.listen((StepCount event) {
            debugPrint('获取计数器今日步数 ${event.steps}');
            if (event.steps == 0) {
              _stepCountStream = DailyPedometer2.stepCountStream;
              _stepCountStream.listen((StepCount totalEvent) {
                debugPrint('获取计数器全部步数 ${totalEvent.steps}');
              }).onError((e) {
                debugPrint('获取计数器全部步数失败 $e');
              });
            }
          }).onError((e) {
            debugPrint('获取计数器今日步数失败 $e');
          }); */
          if (!mounted) return;
          final pedometer = DailyPedometer();
          await pedometer.initialize(false);
          stepCount = pedometer.steps;
          _addStepCount();
          debugPrint('今日步数: $stepCount');
        }
      } catch (e) {
        debugPrint('获取计数器步数失败: $e');
      }
    } else {
      // iOS 平台直接尝试获取步数
      bool authorized = await HealthKitChannel().isHealthKitAuthorized();
      if (authorized) {
        stepPermission = true;
        final now = DateTime.now();
        final midnight = DateTime(now.year, now.month, now.day);
        int steps = await health.getTotalStepsInInterval(midnight, now) ?? 0;
        stepCount = steps;
        _addStepCount();
        debugPrint('今日步数: $stepCount');
      } else {
        stepPermission = false;
        debugPrint('获取今日步数失败');
      }
    }
  }

  Future<void> _requestPermission() async {
    if (Platform.isAndroid) {
      var activityRecognition = await Permission.activityRecognition.status;
      if (!activityRecognition.isGranted) {
        showDialog(
          context: context,
          builder: (context) => CustomDialog(
            title: "提示",
            content: "步数排行需要获取健身运动权限",
            cancelButtonText: "取消",
            confirmButtonText: "确定",
            cancelButtonColor: AppTheme.colorButtonGrey,
            confirmButtonColor: AppTheme.colorBlue,
            onCancel: () {
              Navigator.of(context).pop();
            },
            onConfirm: () async {
              PermissionStatus status =
                  await Permission.activityRecognition.request();
              stepPermission = status == PermissionStatus.granted;
              Navigator.of(context).pop();
            },
          ),
        );
      } else {
        PermissionStatus status =
            await Permission.activityRecognition.request();
        stepPermission = status == PermissionStatus.granted;
      }
    } else {
      bool permission =
          await health.requestAuthorization(types, permissions: permissions);
      if (permission) {
        stepPermission = await HealthKitChannel().isHealthKitAuthorized();
      } else {
        stepPermission = false;
      }
    }
    if (stepPermission != null && stepPermission!) {
      _getTodayStep();
    }
  }

  void _retryRequestPermission() async {
    if (Platform.isAndroid) {
      _requestPermission();
    } else {
      showCupertinoAlert(context);
    }
  }

  void showCupertinoAlert(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text('提示'),
          content: Text('点击‘去授权’后请点击右上角头像->隐私->App->新途径人->开启权限'),
          actions: [
            CupertinoDialogAction(
              child:
                  Text('取消', style: TextStyle(color: const Color(0xFF999999))),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            CupertinoDialogAction(
              child: Text('去授权',
                  style: TextStyle(color: const Color(0xFF0054FF)).pfRegular),
              onPressed: () async {
                Navigator.of(context).pop();
                final url = 'x-apple-health://'; // 替换为你的URL Scheme
                if (await canLaunch(url)) {
                  await launch(url);
                } else {
                  throw 'Cannot launch $url';
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    _subscriptionTabChangeBloc();
    topTabIndex == 1 ? _getRankData() : _getPKData();
    _getTodayStep();
    _requestPermission();
  }

  @override
  void dispose() {
    subscription?.cancel();
    subscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 19.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                  onTap: () => _tabChange(1),
                  child: Image.asset(
                    topTabIndex == 1
                        ? 'assets/png/health/step_check.png'
                        : 'assets/png/health/step_uncheck.png',
                    width: 166.w,
                    height: 58.h,
                  )),
              GestureDetector(
                  onTap: () => _tabChange(2),
                  child: Image.asset(
                    topTabIndex == 2
                        ? 'assets/png/health/health_check.png'
                        : 'assets/png/health/health_uncheck.png',
                    width: 166.w,
                    height: 58.h,
                  )),
            ],
          ),
        ),
        SizedBox(height: 18.h),
        Expanded(
            child: Container(
          color: const Color(0xFFF7F8FD),
          child: MediaQuery.removePadding(
            removeTop: true,
            context: context,
            child: EasyRefresh.builder(
              controller: _refreshController,
              onRefresh: () {
                topTabIndex == 1 ? _getRankData() : _getPKData();
              },
              childBuilder: (_, physic) {
                if (topTabIndex == 1) {
                  return ListView.builder(
                      physics: physic,
                      itemCount: healthRankList.isEmpty
                          ? 1
                          : healthRankList.length + 1,
                      itemBuilder: (_, index) {
                        if (index == 0) {
                          return HealthStepMeItem(
                            stepPermission: stepPermission,
                            item: rankMe,
                            onTap: () => _retryRequestPermission(),
                            sort: healthRankList.indexWhere(
                                (e) => e.walk.user_id == rankMe?.walk.user_id),
                          );
                        }
                        if (healthRankList.isEmpty) {
                          return const NoDataPage();
                        }
                        HealthRankModel item = healthRankList[index - 1];
                        return HealthStepItem(item: item, index: index);
                      });
                } else {
                  return healthPkList.isEmpty
                      ? NoDataPage(
                          physics: physic,
                          img: 'assets/png/study/studying_no_data.png',
                        )
                      : ListView.builder(
                          physics: physic,
                          itemCount: healthPkList.length,
                          itemBuilder: (_, index) {
                            HealthPkModel item = healthPkList[index];
                            return HealthPkItem(item: item, index: index + 1);
                          });
                }
              },
            ),
          ),
        )),
      ],
    );
  }
}
