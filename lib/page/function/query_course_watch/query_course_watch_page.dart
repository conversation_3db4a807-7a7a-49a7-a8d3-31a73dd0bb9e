import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/model/function/course_query_model.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';

class QueryCourseWatchPage extends StatefulWidget {
  final String title;
  const QueryCourseWatchPage({super.key, required this.title});

  @override
  State<QueryCourseWatchPage> createState() => _QueryCourseWatchPageState();
}

class _QueryCourseWatchPageState extends State<QueryCourseWatchPage> {
  TextEditingController _textEditingController = TextEditingController();
  FocusNode _focusNode = FocusNode();

  List<String> dropList = ['今日', '昨日', '本周', '本月', '自定义'];
  bool dropIsOpen = false;
  String? selectedValue;

  DateTime _startTime = DateTime.now();
  DateTime _endTime = DateTime.now();

  String registerAt = '';
  bool isRegister = true;

  CourseQueryModel? model;

  List<MenuItemButton> get dropItems =>
      dropList.map((e) => _menuItem(e)).toList();

  String get queryStr => _textEditingController.text;

  void _query() {
    if (_textEditingController.text.isEmpty) {
      return;
    }
    if (!ValidatorUtils.isPhoneNumber(_textEditingController.text)) {
      EasyLoading.showToast('请输入正确手机号');
      return;
    }
    EasyLoading.show();
    String start = _startTime.toString().split(' ').first;
    String end = _endTime.toString().split(' ').first;
    UserServiceProvider()
        .queryUserWatch(_textEditingController.text, start, end)
        .then((value) {
      EasyLoading.dismiss();
      if (value?.code == 200) {
        model = CourseQueryModel.formJson(value?.data);
        registerAt = model?.registerAt ?? '';
        isRegister = true;
      } else if (value?.code == 400) {
        registerAt = '';
        isRegister = false;
      }
      setState(() {});
    });
  }

  @override
  void initState() {
    super.initState();
    selectedValue = dropList.first;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF9F9F9),
        appBar: CommonNav(title: widget.title),
        body: Column(
          children: [
            Container(
              color: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Column(
                children: [
                  _searchView(),
                  SizedBox(height: 15.h),
                  _dropView(),
                ],
              ),
            ),
            if (queryStr.isEmpty) _normalView(),
            if (queryStr.isNotEmpty && !isRegister) _noRegisterView(),
            if (queryStr.isNotEmpty && isRegister)
              Expanded(child: _contentView()),
            SizedBox(height: ScreenUtil().bottomBarHeight),
          ],
        ),
      ),
    );
  }

  Widget _searchView() {
    return Stack(
      alignment: Alignment.centerRight,
      children: [
        TextField(
          controller: _textEditingController,
          focusNode: _focusNode,
          maxLength: 11,
          keyboardType: TextInputType.phone,
          onChanged: (value) {
            if (value.isEmpty) {
              setState(() {});
            }
          },
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.all(10),
            counter: const Offstage(),
            hintText: '请输入手机号',
            hintStyle: TextStyle(
              color: const Color(0xFFCCCCCC),
              fontSize: 15.sp,
            ).pfMedium,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: Color(0xFF333333),
                width: 1.5,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: Color(0xFF333333),
                width: 1.5,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: Color(0xFF333333),
                width: 1.5,
              ),
            ),
            prefixIcon: Container(
              width: 50.w,
              alignment: Alignment.center,
              child:
                  Image.asset('assets/png/search.png', width: 17, height: 17),
            ),
            suffixIcon: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => _query(),
                child: Container(
                  margin: EdgeInsets.only(right: 8.w),
                  width: 50.w,
                  alignment: Alignment.center,
                  child: Text(
                    '查询',
                    style: TextStyle(
                            color: AppTheme.colorBlue,
                            fontSize: 15.sp,
                            height: 17.58.sp / 15.sp)
                        .pfMedium,
                  ),
                )),
          ),
          style: TextStyle(
            fontSize: 15.sp,
          ),
        ),
        Positioned(
          right: 55.w,
          child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                _textEditingController.clear();
                registerAt = '';
                setState(() {});
              },
              child: Container(
                width: 27,
                height: 27,
                alignment: Alignment.center,
                child: Image.asset(
                  'assets/png/function/watch_course_query/clear.png',
                  width: 17,
                  height: 17,
                ),
              )),
        )
      ],
    );
  }

  Widget _dropView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        registerAt.isEmpty
            ? const SizedBox()
            : Expanded(
                child: Text(
                '注册时间: $registerAt ${model?.teacher == null || model!.teacher!.isEmpty ? '' : '(${model!.teacher})'}',
                style:
                    TextStyle(color: const Color(0xFF979797), fontSize: 12.sp)
                        .pfMedium,
              )),
        SizedBox(width: 16.w),
        MenuAnchor(
          style: MenuStyle(
            visualDensity: VisualDensity.compact,
            backgroundColor: const WidgetStatePropertyAll(Colors.white),
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
                side: const BorderSide(color: Color(0xFFDADADA), width: 0.5),
              ),
            ),
            elevation: const WidgetStatePropertyAll(0),
          ),
          builder:
              (BuildContext context, MenuController controller, Widget? child) {
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                controller.isOpen ? controller.close() : controller.open();
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                decoration: BoxDecoration(
                    color: const Color(0xFFF2F2F2),
                    borderRadius: BorderRadius.circular(8.r)),
                child: Row(
                  children: [
                    Text(
                      selectedValue ?? dropList.first,
                      style: TextStyle(
                              color: const Color(0xFF000000), fontSize: 12.sp)
                          .pfMedium,
                    ),
                    SizedBox(width: 5.w),
                    Image.asset(
                      dropIsOpen
                          ? 'assets/png/xiaoxin/drop_up.png'
                          : 'assets/png/xiaoxin/drop_down.png',
                      width: 15.w,
                      height: 10.h,
                    ),
                  ],
                ),
              ),
            );
          },
          menuChildren: dropItems,
          onClose: () {
            setState(() {
              dropIsOpen = false;
            });
          },
          onOpen: () {
            setState(() {
              dropIsOpen = true;
            });
          },
        ),
      ],
    );
  }

  MenuItemButton _menuItem(String e) {
    return MenuItemButton(
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 7.5.w, vertical: 8.h),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color: e == '自定义'
                        ? Colors.transparent
                        : const Color(0xFFD9D9D9)))),
        child: Text(
          e,
          style: TextStyle(
                  color: selectedValue == e
                      ? AppTheme.colorBlue
                      : const Color(0xFF000000),
                  fontSize: 12.sp)
              .pfMedium,
        ),
      ),
      onPressed: () {
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
        if (e == '今日') {
          selectedValue = e;
          _startTime = DateTime.now();
          _endTime = DateTime.now();
          _query();
        } else if (e == '昨日') {
          selectedValue = e;
          Map d = DateTimeUtils.getYestorday();
          _startTime = DateTime.parse(d['start']);
          _endTime = DateTime.parse(d['end']);
          _query();
        } else if (e == '本周') {
          selectedValue = e;
          Map d = DateTimeUtils.getCurrentWeek();
          _startTime = DateTime.parse(d['start']);
          _endTime = DateTime.parse(d['end']);
          _query();
        } else if (e == '本月') {
          selectedValue = e;
          Map d = DateTimeUtils.getCurrentMonth();
          _startTime = DateTime.parse(d['start']);
          _endTime = DateTime.parse(d['end']);
          _query();
        } else {
          _pickerDate();
        }
        setState(() {});
      },
    );
  }

  Widget _normalView() {
    return Container(
      margin: EdgeInsets.only(top: 52.h),
      child: Column(
        children: [
          Image.asset('assets/png/function/watch_course_query/normal.png',
              width: 150.w, height: 122.56.h),
          SizedBox(height: 23.5.h),
          Text(
            '请输入学员\n新途径网课平台登录手机号',
            textAlign: TextAlign.center,
            style: TextStyle(color: const Color(0xFF666666), fontSize: 15.sp)
                .pfMedium,
          )
        ],
      ),
    );
  }

  void _pickerDate() async {
    final config = CalendarDatePicker2WithActionButtonsConfig(
        calendarType: CalendarDatePicker2Type.range,
        selectedDayHighlightColor: AppTheme.colorBlue,
        daySplashColor: Colors.transparent,
        hideMonthPickerDividers: true,
        hideYearPickerDividers: true);
    List<DateTime?>? dates = await showCalendarDatePicker2Dialog(
        context: context,
        config: config,
        value: [DateTime.now()],
        dialogSize: Size(ScreenUtil().screenWidth - 64.w, 300.h));
    if (dates != null && dates.isNotEmpty) {
      _startTime = dates.first ?? DateTime.now();
      _endTime = dates.last ?? DateTime.now();
    }
    String start = _startTime.toString().split(' ').first;
    String end = _endTime.toString().split(' ').first;
    selectedValue = '$start\n$end';
    _query();
  }

  Widget _noRegisterView() {
    return Container(
      margin: EdgeInsets.only(top: 52.h),
      child: Column(
        children: [
          Image.asset('assets/png/mine/approval_no_data.png',
              width: 124.25.w, height: 132.5.h),
          SizedBox(height: 23.5.h),
          Text(
            '用户暂未注册',
            textAlign: TextAlign.center,
            style: TextStyle(color: const Color(0xFFA6ADBB), fontSize: 14.sp)
                .pfMedium,
          )
        ],
      ),
    );
  }

  Widget _contentView() {
    bool noData = (model?.items ?? []).isEmpty;
    return noData
        ? Container(
            width: ScreenUtil().screenWidth,
            padding: EdgeInsets.fromLTRB(16.w, 15.h, 16.w, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _contentTitleView(),
                SizedBox(height: 82.5.h),
                Center(
                  child: Image.asset(
                      'assets/png/function/watch_course_query/no_history.png',
                      width: 200.w,
                      height: 126.17.h),
                ),
                SizedBox(height: 23.5.h),
                Center(
                  child: Text(
                    '该用户暂无看课记录',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                            color: const Color(0xFFA6ADBB), fontSize: 14.sp)
                        .pfMedium,
                  ),
                )
              ],
            ),
          )
        : Container(
            width: ScreenUtil().screenWidth,
            padding: EdgeInsets.fromLTRB(16.w, 15.h, 16.w, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _contentTitleView(),
                SizedBox(height: 15.h),
                Expanded(
                    child: ListView.builder(
                        itemCount: model!.items!.length,
                        itemBuilder: _itemBuilder))
              ],
            ),
          );
  }

  Widget _itemBuilder(BuildContext c, int index) {
    ItemModel item = model!.items![index];
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              item.courseName ?? '',
              maxLines: 1,
              style: TextStyle(
                color: const Color(0xFF000000),
                fontSize: 15.sp,
                overflow: TextOverflow.ellipsis,
              ).pfMedium,
            ),
          ),
          SizedBox(height: 12.h),
          Container(
            width: double.infinity,
            height: 0.5,
            color: const Color(0xFFF1F1F1),
          ),
          SizedBox(height: 12.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _itemBottomView(
                    item.liveMinutes ?? 0, '直播看课时长(min)', AppTheme.colorBlue),
                _itemBottomView(
                    item.playMinutes ?? 0, '录播看课时长(min)', AppTheme.colorBlue),
                _itemBottomView(item.totalMinutes ?? 0, '总时长(min)',
                    const Color(0xFF000000)),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _itemBottomView(int number, String title, Color numberColor) {
    return Column(
      children: [
        Text(
          '$number',
          style: TextStyle(color: numberColor, fontSize: 18.sp).pfSemiBold,
        ),
        Text(
          title,
          style: TextStyle(color: const Color(0xFF777777), fontSize: 11.sp)
              .pfMedium,
        ),
      ],
    );
  }

  Widget _contentTitleView() {
    return RichText(
        text:
            TextSpan(text: '共计', style: _contentTitleStyleNormal(), children: [
      TextSpan(
          text: ' ${(model?.items ?? []).length} ',
          style: _contentTitleStyleSpecial()),
      TextSpan(text: '个课程,', style: _contentTitleStyleNormal()),
      TextSpan(text: '看课', style: _contentTitleStyleNormal()),
      TextSpan(
          text: ' ${model?.minutes ?? 0} ', style: _contentTitleStyleSpecial()),
      TextSpan(text: '分钟', style: _contentTitleStyleNormal()),
    ]));
  }

  TextStyle _contentTitleStyleNormal() {
    return TextStyle(color: const Color(0xFF7B7F8A), fontSize: 13.sp).pfMedium;
  }

  TextStyle _contentTitleStyleSpecial() {
    return TextStyle(color: AppTheme.colorBlue, fontSize: 13.sp).pfMedium;
  }
}
