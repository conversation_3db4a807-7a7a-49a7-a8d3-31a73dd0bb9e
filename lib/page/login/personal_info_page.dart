import 'dart:async';

import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';

import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/register_dialog.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';

import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:npemployee/widget/login/depart_tree_dialog.dart';
import 'package:npemployee/widget/login/tree_selection_dialog.dart';

import '../../common/dialog/custom_tips_dialog.dart';
import '../../model/study/department_tree_model.dart';
import '../../widget/login/bottom_privacy_section.dart';
import '../../widget/login/position_selection_dialog.dart';
import '../../widget/login/selection_widget.dart';
import '../../widget/login/single_selection_dialog.dart';

class PersonalInfoPage extends StatefulWidget {
  @override
  _PersonalInfoPageState createState() => _PersonalInfoPageState();
}

class _PersonalInfoPageState extends State<PersonalInfoPage> {
  final FocusNode _focusNode = FocusNode();
  final FocusNode _idFocusNode = FocusNode();
  final FocusNode _mobileNode = FocusNode();
  TextEditingController _mobileTextEditController = TextEditingController();
  TextEditingController _nameTextEditController = TextEditingController();
  TextEditingController _idNumberTextEditController = TextEditingController();

  List? departmentList = [];
  List<String> departments = [];
  List? reviewerList = [];
  List<String> reviewers = [];
  List? roleList = [];
  List<String> roles = [];

  DepartmentModel? currentDepartment;

  bool _isAgreed = false;
  Map<String, List<dynamic>> selectedPositionsMap = {
    '入职时间': [DateTime.now().toString().split(" ").first],
    '选择部门': [],
    '选择岗位': [],
    '选择审批人': [],
  };

  late StreamSubscription<bool> keyboardSubscription;

  @override
  void initState() {
    super.initState();
    _keyboardListener();
    _getData();
    _getPhoneNUmber();
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    super.dispose();
  }

  void _keyboardListener() {
    var keyboardVisibilityController = KeyboardVisibilityController();
    // Subscribe
    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      if (!visible) {
        _nodesUnfocus();
      }
      print('Keyboard visibility update. Is visible: $visible');
    });
  }

  void _nodesUnfocus() {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
    }
    if (_idFocusNode.hasFocus) {
      _idFocusNode.unfocus();
    }
    if (_mobileNode.hasFocus) {
      _mobileNode.unfocus();
    }
  }

  void _getPhoneNUmber() {
    UserServiceProvider().getPhoneNumberRegister().then((v) {
      if (v?.found == true) {
        setState(() {
          _mobileTextEditController.text = v?.mobile;
        });
      } else {
        NavigatorUtils.pop(context);
      }
    });
  }

  void _getData() {
    UserServiceProvider().getDepartmentList().then((value) {
      departmentList = value?.data;
      if (mounted) {
        setState(() {
          departmentList?.forEach((e) {
            departments.add(e['department']['name']);
          });
        });
      }
    });
  }

  void _getRoleAndReviewerByDepartmentId(int departmentId) {
    UserServiceProvider().getRoleList(departmentId).then((value) {
      roleList = value?.data;
      roles.clear();
      setState(() {
        roleList?.forEach((e) {
          e['roles'].forEach((r) {
            roles.add(r['role']['name']);
          });
        });
      });
    });
    UserServiceProvider().getReviewerList(departmentId).then((value) {
      reviewerList = value?.data;
      reviewers.clear();
      setState(() {
        reviewerList?.forEach((e) {
          reviewers.add(e['name']);
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    const double originalWidth = 1125.0;
    const double originalHeight = 714.0;
    final double aspectRatio = originalWidth / originalHeight;
    final double calculatedHeight = screenWidth / aspectRatio;

    return GestureDetector(
      onTap: () {
        _nodesUnfocus();
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.white,
        body: Stack(
          children: [
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Image.asset(
                'assets/png/login/personal_info_bg.png',
                fit: BoxFit.cover,
                width: screenWidth,
                height: calculatedHeight,
              ),
            ),
            Positioned(
              top: 53.h,
              left: 5.w,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => NavigatorUtils.pop(context),
                child: Container(
                  width: 30.w,
                  height: 30.h,
                  alignment: Alignment.center,
                  child: SvgPicture.asset(
                      'assets/svg/mine/personal_info/back.svg',
                      width: 16,
                      height: 16),
                ),
              ),
            ),
            Positioned.fill(
              top: 93.5.h,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildIntroductionSection(),
                    SizedBox(height: 47.5.h),
                    _buildEditableField(
                        '手机号', '', '请输入手机号', _mobileTextEditController,
                        isEditable: false),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    _buildAccountIdField(),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    _buildEditableField(
                        '姓名', '', '请填写本人姓名', _nameTextEditController,
                        isEditable: true),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    _buildEditableField(
                        '身份证号', '', '请填写本人身份证', _idNumberTextEditController,
                        isEditable: true),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    SelectionWidget(
                      title: '入职时间',
                      description: '',
                      clickableText: '',
                      placeholder: '请选择',
                      selectedPositionsMap: selectedPositionsMap,
                      onClickableTextTap: () {},
                      onTap: () {
                        _showSelectionDialog(context, '入职时间');
                      },
                    ),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    SelectionWidget(
                      title: '选择部门',
                      description: '如无部门/校区信息，请查看对接流程',
                      clickableText: '对接流程',
                      placeholder: '请选择部门信息',
                      selectedPositionsMap: selectedPositionsMap,
                      onClickableTextTap: () {
                        NavigatorUtils.push(context, CommonRouter.webview,
                            arguments: {
                              'title': '对接流程',
                              'url':
                                  'https://docs.qq.com/doc/DWlJrYXp5TVFvYWRk',
                              'keep_alive': false,
                            });
                      },
                      onTap: () {
                        _showSelectionDialog(context, '选择部门');
                      },
                    ),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    SelectionWidget(
                      title: '选择岗位',
                      description: '',
                      clickableText: '',
                      placeholder: '校长、管理员、客服',
                      selectedPositionsMap: selectedPositionsMap,
                      onClickableTextTap: () {
                        WeChatService().launchWeChatWork();
                      },
                      onTap: () {
                        if (selectedPositionsMap['选择部门']!.isEmpty) {
                          EasyLoading.showToast('请选择部门');
                          return;
                        }
                        _showSelectionDialog(context, '选择岗位');
                      },
                    ),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    SelectionWidget(
                      title: '选择审批人',
                      description: '新部门/校区暂无负责人，请查看对接流程',
                      clickableText: '对接流程',
                      placeholder: '请选择审批人',
                      selectedPositionsMap: selectedPositionsMap,
                      onClickableTextTap: () {
                        NavigatorUtils.push(context, CommonRouter.webview,
                            arguments: {
                              'title': '对接流程',
                              'url':
                                  'https://docs.qq.com/doc/DWlJrYXp5TVFvYWRk',
                              'keep_alive': false,
                            });
                      },
                      onTap: () {
                        if (selectedPositionsMap['选择部门']!.isEmpty) {
                          EasyLoading.showToast('请选择部门');
                          return;
                        }
                        _showSelectionDialog(context, '选择审批人');
                      },
                    ),
                    _buildDivider(),
                    SizedBox(height: 30.h),
                    BottomPrivacySection(
                      isAgreed: _isAgreed,
                      onAgreementChanged: () {
                        setState(() {
                          _isAgreed = !_isAgreed;
                        });
                      },
                      onSubmitForm: _submitForm,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showSelectionDialog(BuildContext context, String title) async {
    if (title == '选择部门') {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: false,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7,
            child: DepartTreeDialog(
              datas: departmentList!,
              selected: currentDepartment,
              onDismiss: () {},
              onDepartChange: (DepartmentModel? model) {
                currentDepartment = model;
                if (selectedPositionsMap[title]!.isNotEmpty) {
                  if (model?.id != selectedPositionsMap[title]?.first['id']) {
                    /// 切换部门，清空岗位和审批人数据
                    selectedPositionsMap['选择岗位'] = [];
                    selectedPositionsMap['选择审批人'] = [];
                  }
                }
                setState(() {
                  selectedPositionsMap[title] = [model?.toJson()];
                });
                if (selectedPositionsMap[title]!.isNotEmpty) {
                  _getRoleAndReviewerByDepartmentId(
                      selectedPositionsMap[title]!.first['id']);
                }
              },
            ),
          );
        },
      );
      /* showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7,
            child: TreeSelectionDialog(
              departments: departmentList!,
              onSelected: (value) {
                if (selectedPositionsMap[title]!.isNotEmpty) {
                  if (value['id'] != selectedPositionsMap[title]?.first['id']) {
                    /// 切换部门，清空岗位和审批人数据
                    selectedPositionsMap['选择岗位'] = [];
                    selectedPositionsMap['选择审批人'] = [];
                  }
                }
                setState(() {
                  selectedPositionsMap[title] = [value];
                });
                if (selectedPositionsMap[title]!.isNotEmpty) {
                  _getRoleAndReviewerByDepartmentId(
                      selectedPositionsMap[title]!.first['id']);
                }
              },
            ),
            /* SingleSelectionDialog(
              title: title,
              options: departments,
              showSearch: true,
              onSelectedOptionChanged: (selectedOption) {
                setState(() {
                  selectedPositionsMap = {
                    title: [selectedOption],
                    '选择岗位': [],
                    '选择审批人': [],
                  };
                });
                if (_getDepartmentId() != null) {
                  _getRoleAndReviewerByDepartmentId(_getDepartmentId()!);
                }
              },
            ), */
          );
        },
      ); */
    } else if (title == '选择岗位') {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7,
            child: PositionSelectionDialog(
              options: roles,
              onSelectedPositionsChanged: (selectedPositions) {
                setState(() {
                  selectedPositionsMap[title] = selectedPositions;
                });
              },
            ),
          );
        },
      );
    } else if (title == '选择审批人') {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7,
            child: SingleSelectionDialog(
              title: title,
              options: reviewers,
              showSearch: false,
              onSelectedOptionChanged: (selectedOption) {
                setState(() {
                  selectedPositionsMap[title] = [selectedOption];
                });
              },
            ),
          );
        },
      );
    } else if (title == '入职时间') {
      final config = CalendarDatePicker2WithActionButtonsConfig(
          calendarType: CalendarDatePicker2Type.single,
          selectedDayHighlightColor: AppTheme.colorBlue,
          daySplashColor: Colors.transparent,
          hideMonthPickerDividers: true,
          hideYearPickerDividers: true);
      List<DateTime?>? dates = await showCalendarDatePicker2Dialog(
          context: context,
          config: config,
          value: [DateTime.now()],
          dialogSize: Size(ScreenUtil().screenWidth - 64.w, 300.h));
      if (dates != null && dates.isNotEmpty) {
        setState(() {
          selectedPositionsMap[title] = [
            dates.first.toString().split(" ").first
          ];
        });
      }
    }
  }

  Widget _buildIntroductionSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '请填写以下个人信息',
            style: TextStyle(
              fontSize: 25.sp,
              color: const Color(0xFF222222),
            ).phBold,
          ),
          SizedBox(height: 5.h),
          Text(
            '请认真填写以下信息，填写后信息不可修改',
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF8B90A0),
            ).pfRegular,
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: Divider(
        color: Color(0xFFE6E6E6),
        thickness: 0.5,
      ),
    );
  }

  /* Widget _buildPhoneNumberField() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('手机号',
              style:
                  TextStyle(color: Color(0xFF222222), fontSize: 17.sp).phBold),
          SizedBox(height: 16.h),
          Text(
            '${GlobalPreferences().userLoginModel?.mobile}',
            style: AppTheme.getTextStyle(
              baseSize: 15.sp,
              color: Color(0xFF606266),
            ),
          ),
        ],
      ),
    );
  } */

  Widget _buildAccountIdField() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('账号ID',
              style: AppTheme.getTextStyle(
                      baseSize: 17.sp, color: AppTheme.colorBlack2)
                  .pfSemiBold),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${GlobalPreferences().userLoginModel?.guid}',
                style: AppTheme.getTextStyle(
                  baseSize: 15.sp,
                  color: Color(0xFFBABABA),
                ).pfRegular,
              ),
              GestureDetector(
                  onTap: () {
                    String accountId =
                        '${GlobalPreferences().userLoginModel?.guid}';
                    Clipboard.setData(ClipboardData(text: accountId));
                    EasyLoading.showToast('账号ID已复制到剪贴板');
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: 30,
                    child: SvgPicture.asset(
                      'assets/svg/mine/copy_icon.svg',
                      width: 15,
                      height: 15,
                    ),
                  )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditableField(String label, String initialValue, String hintText,
      TextEditingController c,
      {bool isEditable = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppTheme.getTextStyle(
                    baseSize: 17.sp, color: AppTheme.colorBlack2)
                .pfSemiBold,
          ),
          SizedBox(height: 16.h),
          TextField(
            controller: c,
            maxLength: c == _mobileTextEditController
                ? 11
                : (c == _idNumberTextEditController ? 18 : null),
            focusNode: c == _nameTextEditController
                ? _focusNode
                : (c == _idNumberTextEditController
                    ? _idFocusNode
                    : _mobileNode),
            enabled: isEditable,
            cursorColor: AppTheme.primaryColor,
            style:
                TextStyle(color: Color(0xFF606266), fontSize: 15.sp).pfRegular,
            decoration: InputDecoration(
              counter: const Offstage(),
              isCollapsed: true,
              border: InputBorder.none,
              hintText: hintText,
              hintStyle: TextStyle(color: Color(0xFFBABABA), fontSize: 15.sp)
                  .pfRegular,
            ),
            onEditingComplete: () {
              _nodesUnfocus();
            },
          ),
        ],
      ),
    );
  }

  List<int>? _getRoleId() {
    List<int>? ids = [];
    selectedPositionsMap['选择岗位']?.forEach((select) {
      roleList?.forEach((e) {
        e['roles'].forEach((r) {
          if (r['role']['name'] == select) {
            ids.add(r['id']);
          }
        });
      });
    });
    return ids;
  }

  String _getReviewerGuid() {
    String guid = '';
    reviewerList?.forEach((e) {
      if (e['name'] == selectedPositionsMap['选择审批人']?.first) {
        guid = e['guid'];
      }
    });
    return guid;
  }

  void _submitForm() {
    if (_mobileTextEditController.text.isEmpty ||
        !ValidatorUtils.isPhoneNumber(_mobileTextEditController.text)) {
      EasyLoading.showToast('手机号格式错误');
      return;
    }
    if (_nameTextEditController.text.isEmpty) {
      EasyLoading.showToast('请输入姓名');
      return;
    }
    if (_idNumberTextEditController.text.isEmpty ||
        !ValidatorUtils.validate(_idNumberTextEditController.text)) {
      EasyLoading.showToast('身份证号格式错误');
      return;
    }
    if (selectedPositionsMap['选择部门']!.isEmpty) {
      EasyLoading.showToast('请选择部门');
      return;
    }
    if (selectedPositionsMap['选择岗位']!.isEmpty) {
      EasyLoading.showToast('请选择岗位');
      return;
    }
    if (selectedPositionsMap['选择审批人']!.isEmpty) {
      EasyLoading.showToast('选择审批人');
      return;
    }
    showDialog(
        context: context,
        builder: (_) {
          return RegisterConfirmDialog(
            name: _nameTextEditController.text,
            depart: selectedPositionsMap['选择部门']?.first,
            mobile: _mobileTextEditController.text,
            id_no: _idNumberTextEditController.text,
            join_at: selectedPositionsMap['入职时间']?.first,
            reviewer_name: selectedPositionsMap['选择审批人']?.first,
            onClick: (v) {
              if (v == 'register') {
                _submitRegister();
              }
            },
          );
        });
  }

  void _submitRegister() {
    EasyLoading.show();

    Map<String, dynamic>? params = {
      'mobile': _mobileTextEditController.text,
      'name': _nameTextEditController.text,
      'id_no': _idNumberTextEditController.text,
      'dep_role_ids': _getRoleId(),
      'reviewer_guid': _getReviewerGuid(),
      'avatar': GlobalPreferences().userLoginModel?.avatar,
      'join_at': '${selectedPositionsMap['入职时间']?.first} 00:00:00'
    };

    UserServiceProvider().commitRegister(params).then((value) {
      EasyLoading.dismiss();
      if (value?.code == 0) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return CustomTipsDialog(
              imagePath: 'assets/png/commit_success.png',
              title: '信息提交成功\n请耐心等待审核',
              content: '审核人：${selectedPositionsMap['选择审批人']?.first}',
              isSystem: _getReviewerGuid() == 'system_approval',
              status: 'review_not_pass',
              onPressed: () {},
            );
          },
        );
      } else {
        EasyLoading.showError(value?.msg ?? '提交失败, 请联系管理员');
      }
    });
  }
}
