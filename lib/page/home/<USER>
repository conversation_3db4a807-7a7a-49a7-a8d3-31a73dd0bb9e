import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/custom_dialog.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/mine/user_sub_model.dart';
import 'package:npemployee/model/study/xiaoxin_menu_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/tabs.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:tap_debouncer/tap_debouncer.dart';

class UserSubPage extends StatefulWidget {
  const UserSubPage({super.key});

  @override
  State<UserSubPage> createState() => _UserSubPageState();
}

class _UserSubPageState extends State<UserSubPage> {
  UserSubModel? userSubModel;

  final List<XiaoxinMenusModel> menus = [];

  void _getMenuList() {
    UserServiceProvider().getSubMenuListAsync().then((value) {
      _formatMenuListData(value);
    });
  }

  _formatMenuListData(ResultData? value) {
    menus.clear();
    for (var e in value?.data) {
      menus.add(XiaoxinMenusModel(
          id: e['id'],
          icon: e['icon'],
          jump: e['jump'] ?? [],
          name: e['name'],
          sort: e['sort'],
          style: e['style']));
    }
    menus.sort((a, b) => a.sort.compareTo(b.sort));

    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    userSubModel = GlobalPreferences().userSubInfo;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getMenuList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFF9F9F9),
        appBar: CommonNav(title: '新途径人客服号', enableBack: false),
        body: SizedBox(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight - 88.h,
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(height: 33.h),
                ClipOval(
                  child: CachedNetworkImage(
                      imageUrl: userSubModel?.user?.avatar ?? '',
                      width: 65,
                      height: 65),
                ),
                SizedBox(height: 12.h),
                Text(GlobalPreferences().userSubInfo?.user?.name ?? '',
                    style: TextStyle(
                            color: const Color(0xFF000000), fontSize: 20.sp)
                        .pfSemiBold),
                SizedBox(height: 4.h),
                Text('当前登录账号为个人名下客服账号',
                    style: TextStyle(
                            color: const Color(0xFF999999), fontSize: 12.sp)
                        .pfRegular),
                SizedBox(height: 21.h),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      width: 343.w,
                      height: 76.h,
                      child: Image.asset(
                          'assets/png/home/<USER>',
                          fit: BoxFit.cover),
                    ),
                    Column(
                      children: [
                        Text(
                          '当前登录客服号: ${userSubModel?.sub_mobile}',
                          style: TextStyle(
                                  color: const Color(0xFFFFFFFF),
                                  fontSize: 15.sp,
                                  height: 17.58.sp / 15.sp)
                              .pfSemiBold,
                        ),
                        SizedBox(height: 6.5.h),
                        Text(
                          '主账号: ${userSubModel?.user?.mobile}',
                          style: TextStyle(
                                  color: const Color(0xFFFFFFFF),
                                  fontSize: 13.sp,
                                  height: 15.23.sp / 13.sp)
                              .pfRegular,
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(height: 12.5.h),
                SizedBox(
                  width: 333.5.w,
                  child: Text('注：每位新途径人仅可注册一个新途径人账号，其他客服账号绑定至本人名下，即可正常记录增长数据。',
                      style:
                          TextStyle(color: AppTheme.colorBlue, fontSize: 13.sp)
                              .pfRegular),
                ),
                if (menus.isNotEmpty) SizedBox(height: 16.h),
                if (menus.isNotEmpty)
                  Container(
                    width: 343.w,
                    padding: EdgeInsets.fromLTRB(16.w, 21.h, 16.w, 16.h),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(17.r),
                        boxShadow: [
                          BoxShadow(
                              color: Color(0xFF000000).withOpacity(0.07),
                              offset: Offset(0, 4),
                              blurRadius: 27.5,
                              spreadRadius: 0),
                        ]),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            SizedBox(width: 13.w),
                            Text('功能',
                                style: TextStyle(
                                        color: const Color(0xFF2B2B2B),
                                        fontSize: 16.sp)
                                    .pfSemiBold),
                          ],
                        ),
                        _buildSimpleIconGrid(context, items: [
                          ...menus.map((e) {
                            bool isDev = false;
                            bool isOpen = true;

                            if (e.style != null && e.style!.isNotEmpty) {
                              if (e.style!.first['key'] == 'isDev') {
                                isDev = bool.tryParse(e.style!.first['value'])!;
                              } else if (e.style!.first['key'] == 'isOpen') {
                                isOpen =
                                    bool.tryParse(e.style!.first['value'])!;
                              }
                            }
                            return TapDebouncer(onTap: () async {
                              NavigatorPushUtils.to(context, e.jump, e.style,
                                  () {
                                _getMenuList();
                              });
                            }, builder: (_, TapDebouncerFunc? onTap) {
                              return ItemView(
                                  title: e.name,
                                  image: e.icon,
                                  isDev: isDev,
                                  isOpen: isOpen,
                                  onTap: onTap);
                            });
                          }),
                        ]),
                      ],
                    ),
                  ),
                SizedBox(height: 16.h),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    _showExitDialog(context);
                  },
                  child: Container(
                    width: 343.w,
                    height: 60.h,
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(12.r),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF000000).withOpacity(0.02),
                            offset: Offset(0, 6.5),
                            blurRadius: 30,
                            spreadRadius: -4,
                          )
                        ]),
                    child: Row(
                      children: [
                        Image.asset('assets/png/home/<USER>',
                            width: 23.5, height: 23.5),
                        SizedBox(width: 15.5.w),
                        Text(
                          '退出登录',
                          style: TextStyle(
                                  color: const Color(0xFF333333),
                                  fontSize: 15.sp)
                              .pfMedium,
                        ),
                        Expanded(child: SizedBox()),
                        Image.asset('assets/png/arrow_right.png',
                            width: 13, height: 13),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 41.h),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    _showUnBindingDialog(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(20),
                    child: Text(
                      '解除绑定',
                      style: TextStyle(
                              color: const Color(0xFFF33A06), fontSize: 15.sp)
                          .pfMedium,
                    ),
                  ),
                ),
                SizedBox(height: 100.h),
              ],
            ),
          ),
        ));
  }

  Widget _buildSimpleIconGrid(BuildContext context,
      {required List<Widget> items}) {
    return Padding(
      padding: const EdgeInsets.only(top: 0, left: 0),
      child: Wrap(
        spacing: 14.w,
        runSpacing: 14.w,
        children: items,
      ),
    );
  }

  void _showExitDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CustomDialog(
        title: "提示",
        content: "是否确认退出登录？",
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        cancelButtonColor: AppTheme.colorButtonGrey,
        confirmButtonColor: AppTheme.colorBlue,
        onCancel: () {
          Navigator.of(context).pop();
          print("Cancellation aborted");
        },
        onConfirm: () {
          AppInfo().clearLoginStatu();
          Navigator.pushNamedAndRemoveUntil(
              context, CommonRouter.loginPage, (route) => false);
        },
      ),
    );
  }

  void _showUnBindingDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CustomDialog(
        title: "提示",
        content: "解除后子账号增长等数据将不再与主账号关联，是否确认解除绑定？",
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        cancelButtonColor: AppTheme.colorButtonGrey,
        confirmButtonColor: AppTheme.colorBlue,
        onCancel: () {
          Navigator.of(context).pop();
          print("Cancellation aborted");
        },
        onConfirm: () {
          UserServiceProvider().subUserDeleteRelation().then((res) {
            if (res?.code == 0) {
              GlobalPreferences().userSubInfo = null;
              Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (_) {
                return const Tabs(index: 4);
              }), (route) => false);
            } else {
              EasyLoading.showToast('解除绑定失败，请联系管理员');
              return;
            }
          });
        },
      ),
    );
  }
}

class ItemView extends StatelessWidget {
  final String title;
  final String image;
  final bool? isDev;
  final bool? isOpen;
  final VoidCallback? onTap;
  const ItemView({
    super.key,
    required this.title,
    required this.image,
    this.isDev,
    this.isOpen,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (isDev ?? false) {
          EasyLoading.showInfo('功能开发中，敬请期待');
        } else if (!isOpen!) {
          EasyLoading.showInfo('活动暂未开启');
        } else {
          if (onTap != null) {
            onTap!();
          }
        }
      }, // Add the callback for the tap event
      child: Stack(
        children: [
          Container(
            width: (ScreenUtil().screenWidth - 64.w - (14.w * 3)) / 4,
            // height: 70,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 16),
                CachedNetworkImage(
                  imageUrl: image,
                  width: 25,
                  height: 25,
                  placeholder: (context, url) {
                    return Container(
                      decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(10.r)),
                      width: 25,
                      height: 25,
                    );
                  },
                ),
                /* Image.asset(
                  image,
                  height: 25,
                  width: 25,
                ), */
                const SizedBox(height: 10),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                          fontSize: 12.sp, color: AppTheme.colorBlackTitle)
                      .pfRegular,
                ),
                SizedBox(height: 11.h),
              ],
            ),
          ),
          if (isDev ?? false)
            Positioned(
              right: 0,
              child: SvgPicture.asset(
                'assets/svg/study/developing.svg',
                height: 15,
              ),
            )
          else if (!isOpen!)
            Positioned(
              right: 0,
              child: SvgPicture.asset(
                'assets/png/function/ic_noopen.svg', // Assuming you have a different asset for open state
                height: 15,
              ),
            ),
        ],
      ),
    );
  }
}
