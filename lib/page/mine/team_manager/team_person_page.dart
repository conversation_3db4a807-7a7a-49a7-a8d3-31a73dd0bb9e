import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/mine/team_manager_department_model.dart';
import 'package:npemployee/model/mine/team_manager_person_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';

class TeamPersonPage extends StatefulWidget {
  final DepartmentModel? departmentModel;
  const TeamPersonPage({super.key, this.departmentModel});

  @override
  State<TeamPersonPage> createState() => _TeamPersonPageState();
}

class _TeamPersonPageState extends State<TeamPersonPage> {
  List<TeamManagerPersonModel> teamManagerPersons = [];

  int page = 1;
  int size = 10;

  final EasyRefreshController _refreshController = EasyRefreshController(
      controlFinishRefresh: true, controlFinishLoad: true);

  void _getTeamManagerPersons({bool isRefresh = false}) {
    if (isRefresh) {
      page = 1;
      teamManagerPersons.clear();
    }
    UserServiceProvider().getTeamManagerPersons(
      widget.departmentModel!.id,
      page,
      size,
      cacheCallBack: (data) {
        // _formatTeamManagerPersonsData(data, true, isRefresh);
      },
      successCallBack: (data) {
        _formatTeamManagerPersonsData(data, false, isRefresh);
      },
      errorCallBack: (data) {
        if (mounted) {
          if (isRefresh) {
            _refreshController.finishRefresh(IndicatorResult.fail);
          } else {
            _refreshController.finishLoad(IndicatorResult.fail);
          }
        }
      },
    );
  }

  void _formatTeamManagerPersonsData(
      ResultData data, bool isCache, bool isRefresh) {
    for (var element in data.data) {
      teamManagerPersons.add(TeamManagerPersonModel.formJson(element));
    }
    bool hasMore = ValidatorUtils.listHasMore(data.count, page, size);

    if (isRefresh) {
      _refreshController.finishRefresh(IndicatorResult.success);
    } else {
      _refreshController.finishLoad(
          hasMore ? IndicatorResult.success : IndicatorResult.noMore);
    }

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getTeamManagerPersons(isRefresh: true);
  }

  @override
  void didUpdateWidget(covariant TeamPersonPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.departmentModel?.id != widget.departmentModel?.id) {
      _getTeamManagerPersons(isRefresh: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return teamManagerPersons.isEmpty
        ? const NoDataPage()
        : MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: Container(
              margin: EdgeInsets.only(top: 16.h),
              child: EasyRefresh.builder(
                  controller: _refreshController,
                  onRefresh: () {
                    _getTeamManagerPersons(isRefresh: true);
                  },
                  onLoad: () {
                    page++;
                    _getTeamManagerPersons(isRefresh: false);
                  },
                  childBuilder: (_, physics) {
                    return ListView.builder(
                      physics: physics,
                      itemBuilder: _itemBuilder,
                      itemCount: teamManagerPersons.length,
                    );
                  }),
            ));
  }

  Widget _itemBuilder(c, index) {
    TeamManagerPersonModel model = teamManagerPersons[index];
    return Container(
        margin: EdgeInsets.only(bottom: 12.h, left: 16.w, right: 16.w),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                  offset: const Offset(0, 4),
                  blurRadius: 15,
                  spreadRadius: 4,
                  color: const Color(0xFF002AA9).withOpacity(0.05))
            ]),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(20.r),
                    child: CachedNetworkImage(
                      imageUrl: model.user.avatar,
                      width: 45,
                      height: 45,
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          model.user.name,
                          style: TextStyle(
                                  fontSize: 15.sp,
                                  color: const Color(0xFF323640))
                              .pfSemiBold,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Flexible(
                              child: Text(
                                model.rolesStr,
                                style: TextStyle(
                                        fontSize: 12.sp,
                                        color: const Color(0xFF8B90A0))
                                    .pfRegular,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Flexible(
                              child: Text(
                                ' |   ${model.user.mobile}',
                                style: TextStyle(
                                        fontSize: 12.sp,
                                        color: const Color(0xFF8B90A0))
                                    .pfRegular,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      showDialog(
                          context: context,
                          builder: (_) {
                            return EditDialog(
                              personModel: model,
                              departmentModel: widget.departmentModel!,
                              successCallBack: () {
                                _refreshController.callRefresh();
                              },
                            );
                          });
                    },
                    child: Image.asset('assets/png/mine/team_manager/edit.png',
                        width: 30, height: 30),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              height: 0.5,
              color: const Color(0xFFE6E6E6),
            ),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      showDialog(
                          context: context,
                          builder: (_) => TipDialog(
                                type: model.user.distribution_department_id == 0
                                    ? 1
                                    : 2,
                                personModel: model,
                                departmentModel: widget.departmentModel!,
                                successCallBack: () {
                                  _refreshController.callRefresh();
                                },
                              ));
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 49.h,
                      child: Text(
                        model.user.distribution_department_id == 0
                            ? '启用推广'
                            : '禁用推广',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: model.user.distribution_department_id == 0
                              ? const Color(0xFFF7B500)
                              : const Color(0xFF0054FF),
                        ).pfRegular,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 0.5.w,
                  height: 25.5.h,
                  color: const Color(0xFFF2F2F2),
                ),
                Expanded(
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      showDialog(
                          context: context,
                          builder: (_) => TipDialog(
                                type: model.user.status == 'frozen' ? 4 : 3,
                                personModel: model,
                                departmentModel: widget.departmentModel!,
                                successCallBack: () {
                                  _refreshController.callRefresh();
                                },
                              ));
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 49.h,
                      child: Text(
                        model.user.status == 'frozen' ? '账号启用' : '账号停用',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: model.user.status == 'frozen'
                              ? const Color(0xFF0054FF)
                              : const Color(0xFFE02020),
                        ).pfRegular,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ));
  }
}

class EditDialog extends StatefulWidget {
  final TeamManagerPersonModel personModel;
  final DepartmentModel departmentModel;
  final VoidCallback successCallBack;
  const EditDialog(
      {super.key,
      required this.personModel,
      required this.departmentModel,
      required this.successCallBack});

  @override
  State<EditDialog> createState() => _EditDialogState();
}

class _EditDialogState extends State<EditDialog> {
  final TextEditingController _nameController = TextEditingController();

  List<RolesRolesModel> _selectedPositions = [];

  List<RolesModel> rolesModels = [];

  final FocusNode _nameFocusNode = FocusNode();

//获取岗位
  void _getRoles() {
    EasyLoading.show();
    UserServiceProvider().getRoleList(widget.departmentModel.id).then((value) {
      EasyLoading.dismiss();
      rolesModels.clear();
      for (var element in value?.data ?? []) {
        rolesModels.add(RolesModel.formJson(element));
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _getRoles();
    _nameController.text = widget.personModel.user.name;
    _selectedPositions = widget.personModel.roles.first.roles;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nameFocusNode.dispose();
    super.dispose();
  }

  void _showPositionPicker() {
    if (rolesModels.isEmpty) {
      // 如果没有数据，显示刷新按钮
      showModalBottomSheet(
        context: context,
        builder: (context) {
          return Container(
            width: double.infinity,
            height: 300.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('暂无岗位数据',
                    style: TextStyle(
                            fontSize: 16.sp, color: const Color(0xFF333333))
                        .pfRegular),
                SizedBox(height: 16.h),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    _getRoles();
                  },
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
                    decoration: BoxDecoration(
                      color: const Color(0xFF0054FF),
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Text(
                      '点击刷新',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                      ).pfMedium,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
      return;
    }

    // 修改为多选模式的底部弹窗
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              height: 300.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('选择岗位',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF242424),
                          ).pfRegular),
                      TextButton(
                        onPressed: () {
                          this.setState(() {
                            // 保持引用不变,只更新内容
                            _selectedPositions = List.from(_selectedPositions);
                          });
                          Navigator.pop(context);
                        },
                        child: Text('确定',
                            style: TextStyle(
                                    fontSize: 14.sp,
                                    color: const Color(0xFF0054FF))
                                .pfMedium),
                      ),
                    ],
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: rolesModels.first.roles.length,
                      itemBuilder: (context, index) {
                        final role = rolesModels.first.roles[index];
                        // 通过比较role.role.id来判断是否选中
                        final isSelected = _selectedPositions.any(
                            (selected) => selected.role.id == role.role.id);
                        return ListTile(
                          title: Text(role.role.name,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: const Color(0xFF242424),
                              )),
                          trailing: isSelected
                              ? Icon(Icons.check_box,
                                  color: const Color(0xFF0054FF), size: 18.w)
                              : Icon(Icons.check_box_outline_blank,
                                  color: const Color(0xFF999999), size: 18.w),
                          onTap: () {
                            setState(() {
                              if (isSelected) {
                                _selectedPositions.removeWhere((selected) =>
                                    selected.role.id == role.role.id);
                              } else {
                                _selectedPositions.add(role);
                              }
                            });
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    ).then((_) {
      // 更新主界面显示
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: ScreenUtil().screenWidth - 52.w,
        margin: EdgeInsetsDirectional.symmetric(horizontal: 26.w),
        padding:
            EdgeInsetsDirectional.symmetric(vertical: 22.h, horizontal: 28.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFFFEEE1), Color(0xFFFFFFFF)])),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Center(
              child: Text(
                '信息编辑',
                style: TextStyle(
                    color: const Color(0xFF333333),
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w600),
              ),
            ),
            SizedBox(height: 22.5.h),
            Text('员工姓名',
                style:
                    TextStyle(color: const Color(0xFF333333), fontSize: 15.sp)),
            SizedBox(height: 8.h),
            Container(
                height: 44.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                        color: const Color(0xFF979797).withOpacity(0.4),
                        width: 0.5)),
                child: TextField(
                  controller: _nameController,
                  focusNode: _nameFocusNode,
                  decoration: InputDecoration(
                      hintText: '请输入姓名',
                      hintStyle: TextStyle(
                        color: const Color(0xFF777777),
                        fontSize: 15.sp,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 12.w, vertical: 12.h)),
                  onEditingComplete: () {
                    _nameFocusNode.unfocus();
                  },
                )),
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('员工岗位',
                    style: TextStyle(
                        color: const Color(0xFF333333), fontSize: 15.sp)),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    NavigatorUtils.push(context, CommonRouter.inAppWebViewPage,
                        arguments: {
                          'title': '',
                          'url': 'https://docs.qq.com/doc/DWlJrYXp5TVFvYWRk'
                        });
                  },
                  child: Text(
                    '新增岗位，点击反馈',
                    style: TextStyle(
                        color: const Color(0xFF0054FF),
                        fontSize: 13.sp,
                        decoration: TextDecoration.underline),
                  ),
                )
              ],
            ),
            SizedBox(height: 8.h),
            GestureDetector(
              onTap: _showPositionPicker,
              child: Container(
                width: double.infinity,
                height: 44.h,
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                        color: const Color(0xFF979797).withOpacity(0.4),
                        width: 0.5)),
                child: Text(
                  _selectedPositions.isEmpty
                      ? '请选择岗位'
                      : _selectedPositions.map((e) => e.role.name).join('、'),
                  style: TextStyle(
                    color: _selectedPositions.isNotEmpty
                        ? const Color(0xFF333333)
                        : const Color(0xFF777777),
                    fontSize: 15.sp,
                  ),
                ),
              ),
            ),
            SizedBox(height: 24.h),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      height: 44.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: const Color(0xFFECECEC),
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Text(
                        '取消',
                        style: TextStyle(
                          color: const Color(0xFFB3B3B3),
                          fontSize: 15.sp,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: GestureDetector(
                    onTap: () async {
                      EasyLoading.show();
                      ResultData? result = await UserServiceProvider()
                          .teamManagerEdit(
                              widget.departmentModel.id,
                              widget.personModel.user.guid,
                              _nameController.text,
                              _selectedPositions.map((e) => e.id).toList(),
                              widget.personModel.user.status,
                              widget.personModel.user.promotion_department_id,
                              widget
                                  .personModel.user.distribution_department_id);
                      EasyLoading.dismiss();
                      if (result?.code == 0) {
                        Navigator.pop(context);
                        widget.successCallBack();
                      } else {
                        EasyLoading.showError('${result?.msg}');
                      }
                    },
                    child: Container(
                      height: 44.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        color: const Color(0xFF0054FF),
                      ),
                      child: Text(
                        '确定',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 15.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class TipDialog extends StatelessWidget {
  final int type; // 1: 启用推广 2: 禁用推广 3: 账号停用 4: 账号启用
  final TeamManagerPersonModel personModel;
  final DepartmentModel departmentModel;
  final VoidCallback successCallBack;
  const TipDialog(
      {super.key,
      required this.type,
      required this.personModel,
      required this.departmentModel,
      required this.successCallBack});

  String get _getTipText {
    switch (type) {
      case 1:
        return '启用推广后，员工将可以进行推广';
      case 2:
        return '禁用推广后，员工将不可以继续推广，确认要禁用吗？';
      case 3:
        return '停用后该账号将不能正常使用新途径人，确认要停用该账号吗？';
      case 4:
        return '确定要启用账号吗';
      default:
        return '';
    }
  }

  void _handleConfirm(BuildContext context) async {
    EasyLoading.show();

    try {
      ResultData? result;
      switch (type) {
        case 1: // 启用推广
          result = await UserServiceProvider().teamManagerEdit(
              departmentModel.id,
              personModel.user.guid,
              personModel.user.name,
              personModel.roleIds,
              personModel.user.status,
              // departmentModel.id,
              personModel.user.promotion_department_id,
              departmentModel.id);
          break;
        case 2: // 禁用推广
          result = await UserServiceProvider().teamManagerEdit(
              departmentModel.id,
              personModel.user.guid,
              personModel.user.name,
              personModel.roleIds,
              personModel.user.status,
              personModel.user.promotion_department_id,
              0);
          break;
        case 3: // 停用账号
          result = await UserServiceProvider().teamManagerEdit(
              departmentModel.id,
              personModel.user.guid,
              personModel.user.name,
              personModel.roleIds,
              'frozen',
              personModel.user.promotion_department_id,
              personModel.user.distribution_department_id);
          break;
        case 4: // 启用账号
          result = await UserServiceProvider().teamManagerEdit(
              departmentModel.id,
              personModel.user.guid,
              personModel.user.name,
              personModel.roleIds,
              'normal',
              personModel.user.promotion_department_id,
              personModel.user.distribution_department_id);
          break;
      }

      EasyLoading.dismiss();
      if (result?.code == 0) {
        NavigatorUtils.pop(context);
        // 刷新列表
        successCallBack();
      } else {
        EasyLoading.showError('${result?.msg}');
      }
    } catch (e) {
      EasyLoading.dismiss();
      print('Error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: ScreenUtil().screenWidth - 52.w,
        margin: EdgeInsetsDirectional.symmetric(horizontal: 26.w),
        padding:
            EdgeInsetsDirectional.symmetric(vertical: 22.h, horizontal: 28.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('提示',
                style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF333333))),
            SizedBox(height: 19.h),
            Text(
              _getTipText,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 15.sp, color: const Color(0xFF333333)),
            ),
            SizedBox(height: 25.h),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      height: 44.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: const Color(0xFFECECEC),
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Text(
                        '取消',
                        style: TextStyle(
                          color: const Color(0xFFB3B3B3),
                          fontSize: 15.sp,
                        ).pfMedium,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _handleConfirm(context),
                    child: Container(
                      height: 44.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        color: const Color(0xFF0054FF),
                      ),
                      child: Text(
                        '确定',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 15.sp,
                        ).pfMedium,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
