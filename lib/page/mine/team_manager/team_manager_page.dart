import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/page/team_no_data_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/mine/team_manager_department_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/page/mine/team_manager/team_person_page.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class TeamManagerPage extends StatefulWidget {
  const TeamManagerPage({super.key});

  @override
  State<TeamManagerPage> createState() => _TeamManagerPageState();
}

class _TeamManagerPageState extends State<TeamManagerPage> {
  late PageController _pageController;
  final List<String> _tabs = ['团队介绍', '团队成员', '团队资产'];
  int _tabIndex = 0;
  DepartmentModel? dropDepartment;

  List<TeamManagerDepartmentModel> teamDepartmentList = [];

  void _getTeamManagerDepartments() async {
    UserServiceProvider().getTeamManagerDetartments(cacheCallBack: (data) {
      _formatTeamDepartmentData(data, true);
    }, successCallBack: (data) {
      _formatTeamDepartmentData(data, false);
    }, errorCallBack: (error) {
      EasyLoading.showToast(error.msg);
      if (error.code == 1) {
        NavigatorUtils.pop(context); //无权限直接返回
      }
    });
  }

  _formatTeamDepartmentData(ResultData data, bool isCache) {
    print('==========================');
    print(data.data);
    print('==========================');
    teamDepartmentList.clear();
    for (var e in data.data) {
      teamDepartmentList.add(TeamManagerDepartmentModel.fromJson(e));
    }
    if (teamDepartmentList.isNotEmpty) {
      int targetId = GlobalPreferences().departmentId ?? -1;
      if (targetId == -1) {
        dropDepartment = teamDepartmentList.first.department;
      } else {
        // 查找部门
        DepartmentModel? result =
            findDepartmentById(teamDepartmentList, targetId);
        if (result == null) {
          dropDepartment = teamDepartmentList.first.department;
        } else {
          dropDepartment = result;
        }
      }
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  // 方法一：在 TeamManagerDepartmentModel 列表中查找
  DepartmentModel? findDepartmentById(
      List<TeamManagerDepartmentModel> list, int targetId) {
    for (var item in list) {
      // 检查当前部门
      if (item.department.id == targetId) {
        return item.department;
      }

      // 递归检查子部门
      var result = searchInChildren(item.children, targetId);
      if (result != null) {
        return result;
      }
    }
    return null;
  }

// 方法二：在 DepartmentModel 的 children 中递归查找
  DepartmentModel? searchInChildren(
      List<DepartmentModel> children, int targetId) {
    for (var child in children) {
      // 检查当前部门
      if (child.id == targetId) {
        return child;
      }

      // // 如果当前部门有子部门，递归查找
      // if (child.children != null && child.children!.isNotEmpty) {
      //   var result = searchInChildren(child.children!, targetId);
      //   if (result != null) {
      //     return result;
      //   }
      // }
    }
    return null;
  }

  void _showDepartmentPicker() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      builder: (context) {
        return StatefulBuilder(
          // 使用StatefulBuilder来管理展开状态
          builder: (context, setState) {
            return Container(
              height: 400.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '选择部门',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF242424),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Expanded(
                    child: ListView.builder(
                      itemCount: teamDepartmentList.length,
                      itemBuilder: (context, index) {
                        return _buildDepartmentTree(
                          teamDepartmentList[index],
                          0,
                          (department) {
                            this.setState(() {
                              dropDepartment = department;
                            });
                            Navigator.pop(context);
                          },
                          setState,
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

// 在类中添加一个Set来存储展开的节点ID
  final Set<int> _expandedNodes = {};

  Widget _buildDepartmentTree(
    TeamManagerDepartmentModel model,
    int level,
    Function(DepartmentModel) onSelect,
    StateSetter setState,
  ) {
    bool isExpanded = _expandedNodes.contains(model.department.id);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if (model.children.isNotEmpty) {
              setState(() {
                if (isExpanded) {
                  _expandedNodes.remove(model.department.id);
                } else {
                  _expandedNodes.add(model.department.id);
                }
              });
            }
          },
          child: Container(
            padding: EdgeInsets.only(
              left: (level * 20).w,
              top: 12.h,
              bottom: 12.h,
            ),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: const Color(0xFFEEEEEE),
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                if (model.children.isNotEmpty)
                  Icon(
                    isExpanded ? Icons.arrow_drop_down : Icons.arrow_right,
                    size: 16.sp,
                    color: const Color(0xFF909090),
                  ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    model.department.name,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF242424),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => onSelect(model.department),
                  child: Container(
                    width: 18.w,
                    height: 18.w,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: dropDepartment?.id == model.department.id
                            ? const Color(0xFF0054FF)
                            : const Color(0xFFDDDDDD),
                      ),
                      borderRadius: BorderRadius.circular(4.r),
                      color: dropDepartment?.id == model.department.id
                          ? const Color(0xFF0054FF)
                          : Colors.white,
                    ),
                    child: dropDepartment?.id == model.department.id
                        ? Icon(
                            Icons.check,
                            size: 14.sp,
                            color: Colors.white,
                          )
                        : null,
                  ),
                ),
                SizedBox(width: 16.w),
              ],
            ),
          ),
        ),
        if (isExpanded && model.children.isNotEmpty)
          ...model.children.map((child) => _buildDepartmentTree(
                TeamManagerDepartmentModel([], child),
                level + 1,
                onSelect,
                setState,
              )),
      ],
    );
  }

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _getTeamManagerDepartments();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Stack(
        children: [
          SizedBox(
            width: ScreenUtil().screenWidth,
            height: 326.5.h,
            child: Image.asset('assets/png/mine/team_manager/background.png',
                fit: BoxFit.fitWidth),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const TeamManagerNav(title: '团队管理'),
              SizedBox(height: 10.h),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: _showDepartmentPicker,
                child: Padding(
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 16.w),
                  child: Row(children: [
                    Image.asset('assets/png/mine/team_manager/department.png',
                        width: 15, height: 15),
                    SizedBox(width: 7.5.w),
                    Text(dropDepartment?.name ?? '',
                        style: TextStyle(
                                color: const Color(0xFF242424), fontSize: 13.sp)
                            .pfSemiBold),
                    SizedBox(width: 7.5.w),
                    Image.asset('assets/png/mine/team_manager/drop_down.png',
                        width: 10, height: 10),
                  ]),
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  ..._tabs.map((e) =>
                      _tabItem(e, _tabIndex == _tabs.indexOf(e), e == '团队资产'))
                ],
              ),
              Expanded(
                child: Container(
                  color: Colors.white,
                  child: PageView(
                    physics: _tabIndex == 2
                        ? const NeverScrollableScrollPhysics() // 禁止滑动到第三个tab
                        : const AlwaysScrollableScrollPhysics(),
                    onPageChanged: (value) {
                      if (value == 2) {
                        _pageController.animateToPage(
                          _tabIndex,
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeInOut,
                        );
                        EasyLoading.showInfo('功能开发中，敬请期待');
                        return;
                      }
                      setState(() {
                        _tabIndex = value;
                      });
                    },
                    controller: _pageController,
                    children: [
                      Stack(children: [
                        Positioned.fill(
                          child: _buildIntroTab(dropDepartment),
                        ),
                        Positioned(
                            bottom: 0,
                            child: Image.asset(
                                "assets/png/mine/team_manager/team_manage_bottom.png",
                                width: ScreenUtil().screenWidth))
                      ]),
                      _buildMembersTab(),
                      _buildAssetsTab(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _tabItem(String title, bool isSelected, bool isDeveloping) {
    String imagePath = '';
    if (title == '团队介绍') {
      imagePath = 'assets/png/mine/team_manager/check_left.png';
    } else if (title == '团队成员') {
      imagePath = 'assets/png/mine/team_manager/check_center.png';
    } else {
      imagePath = 'assets/png/mine/team_manager/check_right.png';
    }
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (isDeveloping) {
          EasyLoading.showInfo('功能开发中，敬请期待');
          return;
        }
        setState(() {
          _tabIndex = _tabs.indexOf(title);
        });
        _pageController.animateToPage(_tabIndex,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut);
      },
      child: SizedBox(
        width: ScreenUtil().screenWidth / 3,
        height: 56.h,
        child: Stack(
          fit: StackFit.expand, // 让Stack填充整个Container
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: isSelected
                  ? SizedBox(
                      width: ScreenUtil().screenWidth / 3,
                      height: 41.h,
                      child: Image.asset(imagePath, fit: BoxFit.fill),
                    )
                  : SizedBox(
                      width: ScreenUtil().screenWidth / 3,
                      height: 41.h,
                    ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Text(
                  title,
                  style: TextStyle(
                          color: isSelected
                              ? const Color(0xFF0054FF)
                              : const Color(0xFF909090),
                          fontSize: 16.sp)
                      .pfSemiBold,
                ),
              ),
            ),
            if (isDeveloping)
              Positioned(
                top: 8.h,
                right: 15,
                child: SvgPicture.asset(
                  'assets/svg/study/developing.svg',
                  height: 15,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroTab(DepartmentModel? d) {
    return dropDepartment?.about == null
        ? TeamNoDataPage(department: d)
        : Padding(
            padding: EdgeInsets.symmetric(horizontal: 23.w, vertical: 23.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(),
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        NavigatorUtils.push(
                                context, MineRouter.teamIntroductionEditPage,
                                arguments: {'department': dropDepartment})
                            .then((_) {
                          _getTeamManagerDepartments();
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 14.w, vertical: 4.5.h),
                        decoration: BoxDecoration(
                            color: const Color(0x7FAAC6FF),
                            borderRadius: BorderRadius.circular(8.r)),
                        child: Text('编辑',
                            style: TextStyle(
                                    color: const Color(0xFF0054FF),
                                    fontSize: 12.sp)
                                .pfMedium),
                      ),
                    ),
                  ],
                ),
                Expanded(
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                      Column(children: [
                        SizedBox(height: 17.5.h),
                        Container(
                          constraints: BoxConstraints(
                            minHeight: 140.h,
                          ),
                          padding: const EdgeInsets.all(16.0),
                          width: ScreenUtil().screenWidth,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: const Color(0xFFF5F5F5),
                          ),
                          child: Text(dropDepartment?.about ?? '',
                              style: TextStyle(
                                      color: const Color(0xFF808080),
                                      fontSize: 15.sp)
                                  .pfRegular),
                        ),
                      ]),
                    ]))
              ],
            ));
  }

  Widget _buildMembersTab() {
    return TeamPersonPage(
        departmentModel: dropDepartment); // TODO: Implement members tab
  }

  Widget _buildAssetsTab() {
    return Container(); // TODO: Implement assets tab
  }
}
