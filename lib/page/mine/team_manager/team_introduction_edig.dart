import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/model/mine/team_manager_department_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';

class TeamIntroductionEdig extends StatefulWidget {
  final DepartmentModel? department;
  const TeamIntroductionEdig({super.key, this.department});

  @override
  State<TeamIntroductionEdig> createState() => _TeamIntroductionEdigState();
}

class _TeamIntroductionEdigState extends State<TeamIntroductionEdig> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _controller.text = widget.department?.about ?? '';
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _hideKeyboard() {
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _hideKeyboard,
      child: Scaffold(
        appBar: CommonNav(
          title: '编辑',
          rightWidget: [
            TextButton(
                onPressed: () async {
                  if (_controller.text.isEmpty) {
                    EasyLoading.showToast('请输入团队介绍');
                    return;
                  }

                  _hideKeyboard();
                  EasyLoading.show();
                  ResultData? result = await UserServiceProvider()
                      .teamManagerDepartMembersEdit(
                          widget.department!.id, _controller.text);
                  EasyLoading.dismiss();
                  if (result?.code == 0) {
                    NavigatorUtils.pop(context);
                  } else {
                    EasyLoading.showError(result?.msg ?? '发生错误，请重试...');
                  }
                },
                child: Text(
                  '确认',
                  style: TextStyle(
                      color: const Color(0xFF0054FF), fontSize: 17.sp),
                ))
          ],
        ),
        body: Container(
          margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 15.h),
          padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 15.h),
          height: 202.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.r),
              border: Border.all(color: const Color(0xFFE6E6E6), width: 0.5)),
          child: Stack(
            children: [
              TextField(
                controller: _controller,
                focusNode: _focusNode,
                maxLength: 100,
                maxLines: 6,
                onChanged: (value) {
                  setState(() {});
                },
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: '请输入团队介绍',
                  hintStyle: TextStyle(
                    color: const Color(0xFFB0B5BF),
                    fontSize: 15.sp,
                  ),
                  counterText: '',
                ),
                style: TextStyle(
                  color: const Color(0xFF808080),
                  fontSize: 15.sp,
                ),
              ),
              Positioned(
                right: 0,
                bottom: 6.h,
                child: Text('${_controller.text.length}/100',
                    style: TextStyle(
                        color: const Color(0xFF808080), fontSize: 11.sp)),
              )
            ],
          ),
        ),
      ),
    );
  }
}
