// Start of Selection
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/mine/mission_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';

class MyTaskPage extends StatefulWidget {
  const MyTaskPage({super.key});

  @override
  State<MyTaskPage> createState() => _MyTaskPageState();
}

class _MyTaskPageState extends State<MyTaskPage>
    with AutomaticKeepAliveClientMixin {
  List tags = [
    {
      'type': '全部',
      'status': ['missioning', 'pass', 'fail']
    },
    {
      'type': '已完成',
      'status': ['pass']
    },
    {
      'type': '未完成',
      'status': ['missioning']
    },
  ];
  int tabIndex = 0;
  int page = 1;
  int size = 10;
  late EasyRefreshController _controller;
  late PageController _pageController;
  List<List<MissionListModel>> _allMissionLists = [[], [], []];

  @override
  bool get wantKeepAlive => true;

  void _getMissionList({required int index, bool isRefresh = false}) {
    if (isRefresh) {
      page = 1;
      _allMissionLists[index].clear();
    }
    UserServiceProvider().getMissionList(
      status: tags[index]['status'],
      page: page,
      size: size,
      cacheCallBack: (data) {
        _formatMissionData(data, true, index, isRefresh);
      },
      successCallBack: (data) {
        _formatMissionData(data, false, index, isRefresh);
      },
      errorCallBack: (data) {
        if (mounted) {
          _controller.finishRefresh(IndicatorResult.fail);
          _controller.finishLoad(IndicatorResult.fail);
        }
      },
    );
  }

  void _formatMissionData(
      ResultData data, bool isCache, int index, bool isRefresh) {
    List<MissionListModel> newMissions = [];
    for (var element in data.data ?? []) {
      MissionListModel model = MissionListModel.fromJson(element);
      newMissions.add(model);
    }
    if (isRefresh) {
      _allMissionLists[index].clear();
    }
    _allMissionLists[index].addAll(newMissions);
    bool hasMore = ValidatorUtils.listHasMore(data.count, page, size);
    _controller
        .finishLoad(hasMore ? IndicatorResult.success : IndicatorResult.noMore);
    if (mounted && !isCache) {
      setState(() {});
    }
    _controller.finishRefresh(IndicatorResult.success);
  }

  @override
  void initState() {
    super.initState();
    _controller = EasyRefreshController(
        controlFinishRefresh: true, controlFinishLoad: true);
    _pageController = PageController(initialPage: tabIndex);
    _getMissionList(index: tabIndex, isRefresh: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CommonNav(title: '我的任务'),
      body: Column(
        children: [
          _tabView(),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: tags.length,
              onPageChanged: (index) {
                setState(() {
                  tabIndex = index;
                });
                _getMissionList(index: index, isRefresh: true);
              },
              itemBuilder: (context, index) {
                return EasyRefresh(
                  controller: _controller,
                  onRefresh: () async {
                    _getMissionList(index: index, isRefresh: true);
                  },
                  onLoad: () async {
                    page++;
                    _getMissionList(index: index);
                  },
                  child: _allMissionLists[tabIndex].isEmpty
                      ? const NoDataPage()
                      : _buildMissionList(index),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _tabView() {
    return Container(
      color: Colors.white,
      constraints: BoxConstraints(maxHeight: 36.h),
      margin: EdgeInsets.only(bottom: 12.h),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: tags.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: VideoTabItem(
              title: tags[index]['type'],
              isSelect: tabIndex == index,
              onTap: () {
                _pageController.animateToPage(
                  index,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.ease,
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildMissionList(int index) {
    return ListView.builder(
      itemCount: _allMissionLists[index].length, //
      itemBuilder: (context, i) {
        MissionListModel model = _allMissionLists[index][i];
        // 判断任务是否超期
        bool isOverdue =
            DateTime.parse(model.deadLineTime).isBefore(DateTime.now());
        return GestureDetector(
          onTap: () {
            if (isOverdue && !model.finished) {
              EasyLoading.showToast('任务已超期');
              return;
            }
            NavigatorUtils.push(context, MineRouter.taskDesPage,
                    arguments: {'id': model.missionUser.id, 'model': model})
                .then((v) {
              _getMissionList(index: tabIndex, isRefresh: true);
            });
          },
          child: Container(
            margin: EdgeInsets.fromLTRB(16.w, 0, 10.w, 16.h),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: Stack(
                          children: [
                            SizedBox(
                              width: 119.w,
                              height: 64.h,
                              child: Image.asset(
                                  'assets/png/mine/task/study_task_bac.png'),
                            ),
                            if (model.hasExam)
                              SizedBox(
                                width: 39.w,
                                height: 15.h,
                                child: Image.asset(
                                    'assets/png/mine/task/has_exam.png'),
                              )
                          ],
                        )),
                    // Start Generation Here
                    SizedBox(width: 7.w),
                    Expanded(
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: Text(
                                  model.mission.name,
                                  maxLines: 2,
                                  style: TextStyle(
                                          fontSize: 15.sp,
                                          color: const Color(0xFF323640),
                                          overflow: TextOverflow.ellipsis)
                                      .pfSemiBold,
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  NavigatorUtils.push(
                                      context, MineRouter.taskRankPage,
                                      arguments: {
                                        'missionId':
                                            model.missionUser.mission_id
                                      });
                                },
                                child: Image.asset(
                                    'assets/png/mine/task/tasking.png',
                                    width: 32,
                                    height: 32),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${model.deadLineTime}前完成',
                                style: TextStyle(
                                        fontSize: 11.sp,
                                        color: const Color(0xFF999999))
                                    .pfRegular,
                              ),
                              if (model.missionUser.mission_status == 'pass')
                                Image.asset(
                                  'assets/png/mine/task/task_finish.png',
                                  width: 32,
                                  height: 32,
                                )
                            ],
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
