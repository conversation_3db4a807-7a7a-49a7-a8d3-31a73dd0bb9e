import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:npemployee/main.dart';
import 'package:npemployee/routers/common_router.dart';
import '../common/app_info.dart';
import 'api.dart';

/// <AUTHOR>
/// @project FlutterKit
/// @date 2023/2/11

class TokenInterceptors extends Interceptor {
  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    if (options.method == HttpMethod.POST) {}
    var tokenKey = await AppInfo.getInstance()!.tokenKey;
    var tokenValue =
        //     'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJndWlkIjoiYnV1NWFyNmgwNHJ1ZzJoaWFsajAiLCJuR3VpZCI6IiIsImFwcF9jb2RlIjo5OTksImV4cCI6MTczNzUzOTg5MDAwMCwiaXNzIjoidXNlcl9jZW50ZXIifQ.Ogzz3R4aYV_MTttNOJdvdCJnMvrRhKEUa2dtarNhQ3A';
        await AppInfo.getInstance()!.tokenValue;
    // var tokenKey = 'X-XTJ-UID';
    // var tokenValue = 'test';
    options.headers[tokenKey] = tokenValue;
    if (options.path.contains('/xtj-people/app/department/members/edit')) {
      options.headers['X-XTJ-API-VERSION'] = '2';
    }
    if (options.path.contains('/school_target_status/queryUserWatch')) {
      options.baseUrl = 'https://schtarsta.xtjzx.cn';
    } else {
      options.baseUrl = HttpManager.baseUrl;
    }
    if (!AppInfo().registered && AppInfo().hasLogined) {
      if (options.path.contains('/app/user/register') ||
          options.path.contains('/app/user/info') ||
          options.path.contains('/app/department/list') ||
          options.path.contains('/app/role/list_by_department') ||
          options.path.contains('/app/user/approval/reviewer') ||
          options.path.contains('/app/department/mp_map') ||
          options.path.contains('/xtjUser/myMobile') ||
          options.path.contains('/third_part/sms/send') ||
          options.path.contains('/app/user/approval/myself') ||
          options.path.contains('/xtj-people/app/user/approval/can_ping') ||
          options.path.contains('/xtj-people/app/user/approval/ping') ||
          options.path.contains('/xtjLogin/smsLogin')) {
        return handler.next(options);
      } else {
        DioException e = DioException(
            requestOptions: RequestOptions(extra: {'type': 'reject'}));
        return handler.reject(e);
      }
    }
    return super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    return super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    //token校验
    if (err.response?.statusCode == 403) {
      AppInfo().clearLoginStatu();
      navigatorKey.currentState
          ?.pushNamedAndRemoveUntil(CommonRouter.loginPage, (route) => false);
      return handler.reject(err);
    }
    super.onError(err, handler);
  }
}
