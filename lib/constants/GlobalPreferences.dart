import 'dart:convert';

import 'package:npemployee/model/mine/guest_model.dart';
import 'package:npemployee/model/mine/me_nodel.dart';
import 'package:npemployee/model/mine/old_person_model.dart';
import 'package:npemployee/model/mine/user_login_model.dart';
import 'package:npemployee/model/mine/user_sub_model.dart';

import '../manager/preferences_manager.dart';

/// <AUTHOR>
/// @project FlutterKit
/// @date 9/25/23

class GlobalPreferences {
  static final GlobalPreferences _instance = GlobalPreferences._internal();

  bool? _isAssetValueVisible;
  bool? _showInLargeUnitCache;
  bool? _showScreenshotImportCache;
  bool? _showSavingModuleCache;
  bool? _isPortfolioValueVisible;
  bool? _isExpenseValueVisible;
  String? _tokenValue; // Declare _tokenValue variable.
  bool? _securityProtection;
  String? _digitalPassword;
  UserLoginModel? _userLoginModel; //用户登录数据
  MeModel? _userInfo; //用户个人信息
  OldPersonModel? _oldUserInfo; //老用户信息
  UserSubModel? _userSubInfo; // 子账号用户信息
  GuestModel? _guestModel; //嘉宾个人信息
  ImLoginModel? _imLoginModel; //IM登录数据
  List? _capabilityDownloads; //能力提升下载列表
  String? _departmentName; //用户当前显示部门名称
  int? _departmentId; //用户当前显示部门id
  bool? _shouldShowedProxy = true;//是否应该显示隐私政策获取弹窗

  bool? _isProxyOn;
  String? _proxyIp;
  String? _proxyPort;

  factory GlobalPreferences() {
    return _instance;
  }

  GlobalPreferences._internal();

  Future<void> loadPreferences() async {
    // 从本地加载设置
    this._isAssetValueVisible =
        await PreferencesManager().getShowAssetValue() ?? true;
    this._showInLargeUnitCache =
        await PreferencesManager().getShowAmountInLargeUnit() ?? false;
    this._showScreenshotImportCache =
        await PreferencesManager().getShowScreenshotImport() ?? false;
    this._showSavingModuleCache =
        await PreferencesManager().getShowSavingModule() ?? false;
    this._isPortfolioValueVisible =
        await PreferencesManager().getShowPortfolioValue() ?? true;
    this._isExpenseValueVisible =
        await PreferencesManager().getShowExpenseValue() ?? true;

    this._tokenValue = await PreferencesManager().getTokenValue();
    this._userLoginModel = await PreferencesManager().getUserLoginModel();
    this._userInfo = await PreferencesManager().getUserInfo();
    this._userSubInfo = await PreferencesManager().getUserSubInfo();
    this._guestModel = await PreferencesManager().getGuestInfo();
    this._oldUserInfo = await PreferencesManager().getOldUserInfo();
    this._imLoginModel = await PreferencesManager().getIMLoginInfo();
    this._departmentName = await PreferencesManager().getDepartmentName();
    this._securityProtection =
        await PreferencesManager().getSecurityProtection() ?? false;
    this._digitalPassword = await PreferencesManager().getDigitalPassword();
    this._isProxyOn = await PreferencesManager().getProxyOn();
    this._proxyIp = await PreferencesManager().getProxyIp();
    this._proxyPort = await PreferencesManager().getProxyPort();
    this._shouldShowedProxy =  await PreferencesManager().getShouldShowWelcomeProxyValue() ?? true;
  }

  // 获取资产值是否可见
  bool get isAssetValueVisible => _isAssetValueVisible ?? true;

  // 设置资产值是否可见，并存储到本地
  set isAssetValueVisible(bool value) {
    _isAssetValueVisible = value;
    PreferencesManager().setShowAssetValue(value); // 将值存储到本地
  }

  // 获取是否以大单位显示
  bool get showInLargeUnitCache => _showInLargeUnitCache ?? false;

  bool get showScreenshotImportCache => _showScreenshotImportCache ?? false;

  bool get showSavingModuleCache => _showSavingModuleCache ?? false;

  // 设置是否以大单位显示，并存储到本地
  set showInLargeUnitCache(bool value) {
    _showInLargeUnitCache = value;
    PreferencesManager().setShowAmountInLargeUnit(value); // 将值存储到本地
  }

  set showScreenshotImport(bool value) {
    _showScreenshotImportCache = value;
    PreferencesManager().setShowScreenshotImport(value); // 将值存储到本地
  }

  set showSavingModule(bool value) {
    _showSavingModuleCache = value;
    PreferencesManager().setShowSavingModule(value); // 将值存储到本地
  }

  bool get securityProtection => _securityProtection ?? false;
  set securityProtection(bool value) {
    _securityProtection = value;
    PreferencesManager().setSecurityProtection(value);
  }

  String? get digitalPassword => _digitalPassword;
  set digitalPassword(String? value) {
    _digitalPassword = value;
    PreferencesManager().setDigitalPassword(value);
  }

  bool get isShouldShowProxy => _shouldShowedProxy ?? true;

   set isShouldShowProxy(bool value){
    _shouldShowedProxy = value;
    PreferencesManager().setShouldShowWelcomeProxyValue(value);
  }


  bool get isPortfolioValueVisible => _isPortfolioValueVisible ?? true;

  set isPortfolioValueVisible(bool value) {
    _isPortfolioValueVisible = value;
    PreferencesManager().setShowPortfolioValue(value);
  }

  bool get isExpenseValueVisible => _isExpenseValueVisible ?? true;

  set isExpenseValueVisible(bool value) {
    _isExpenseValueVisible = value;
    PreferencesManager().setShowExpenseValue(value);
  }

  // Getter for tokenValue
  String? get tokenValue => _tokenValue;

  // Setter for tokenValue and store it in preferences
  set tokenValue(String? value) {
    _tokenValue = value;
    PreferencesManager().setTokenValue(value ?? '');
    if (value != null) {
      PreferencesManager().setTokenValue(value);
    } else {
      PreferencesManager().removeTokenValue();
    }
  }

  UserLoginModel? get userLoginModel => _userLoginModel;

  set userLoginModel(UserLoginModel? model) {
    _userLoginModel = model;
    String? jsonStr = jsonEncode(model!.toJson());
    PreferencesManager().setUserLoginModel(jsonStr);
  }

  MeModel? get userInfo => _userInfo;

  set userInfo(MeModel? user) {
    _userInfo = user;
    if (user != null) {
      PreferencesManager().setUserInfo(user);
    } else {
      PreferencesManager().removeUserInfo();
    }
  }

  UserSubModel? get userSubInfo => _userSubInfo;
  set userSubInfo(UserSubModel? model) {
    _userSubInfo = model;
    if (model != null) {
      PreferencesManager().setUserSubInfo(model);
    } else {
      PreferencesManager().removeUserSubInfo();
    }
  }

  GuestModel? get guestInfo => _guestModel;

  set guestInfo(GuestModel? model) {
    _guestModel = model;
    if (model != null) {
      PreferencesManager().setGuestInfo(model);
    } else {
      PreferencesManager().removeGuestInfo();
    }
  }

  OldPersonModel? get oldUserInfo => _oldUserInfo;
  set oldUserInfo(OldPersonModel? model) {
    _oldUserInfo = model;
    if (model != null) {
      PreferencesManager().setOldUserInfo(model);
    } else {
      PreferencesManager().removeOldUserInfo();
    }
  }

  ImLoginModel? get imLoginModel => _imLoginModel;
  set imLoginModel(ImLoginModel? model) {
    _imLoginModel = model;
    if (model != null) {
      PreferencesManager().setImLoginInfo(model);
    } else {
      PreferencesManager().removeImLoginInfo();
    }
  }

  String? get departmentName => _departmentName;

  set departmentName(String? value) {
    _departmentName = value;
    if (value != null) {
      PreferencesManager().setDepartmentName(value);
    } else {
      PreferencesManager().removeDepartmentName();
    }
  }

  int? get departmentId => _departmentId;

  set departmentId(int? value) {
    _departmentId = value;
    if (value != null) {
      PreferencesManager().setDepartmentId(value);
    } else {
      PreferencesManager().removeDepartmentId();
    }
  }

  List? get capabilityDownloads => _capabilityDownloads;

  bool get isProxyOn => _isProxyOn ?? false;
  set isProxyOn(bool? value) {
    _isProxyOn = value;
    if (value != null) {
      PreferencesManager().setProxyOn(value);
    } else {
      PreferencesManager().removeProxyOn();
    }
  }

  String? get proxyIp => _proxyIp;
  set proxyIp(String? value) {
    _proxyIp = value;
    if (value != null) {
      PreferencesManager().setProxyIp(value);
    } else {
      PreferencesManager().removeProxyIp();
    }
  }

  String? get proxyPort => _proxyPort;
  set proxyPort(String? value) {
    _proxyPort = value;
    if (value != null) {
      PreferencesManager().setProxyPort(value);
    } else {
      PreferencesManager().removeProxyPort();
    }
  }
}
