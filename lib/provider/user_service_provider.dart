import 'dart:io'; // Import this for platform detection.
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/local_cache_manager.dart';
import 'package:npemployee/network/api_extension.dart';
import 'package:package_info/package_info.dart';
import '../common/app_info.dart';
import '../model/mine/user_login_model.dart';
import '../network/api.dart';
import '../network/code.dart';
import '../network/result_data.dart';
import '../service/wechat_service.dart';

class UserServiceProvider {
  /// 获取验证码
  Future<ResultData?> requestSmsCode(String mobile) async {
    // Get the current time in UTC and adjust it to Beijing time (UTC+8)
    DateTime now = DateTime.now().toUtc().add(Duration(hours: 8));

    // Format the time to 'yyyy-MM-dd HH:mm:ss' in Beijing time
    String time = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);

    String sign = httpManager.generateMd5("${mobile}xtjnb${time}");

    var params = {
      "mobile": mobile,
      "time": time,
      "sign": sign,
    };

    ResultData resultData = await httpManager.requestUrl(
      "/third_part/sms/send",
      method: HttpMethod.POST,
      params: params,
    );

    return resultData;
  }

  /// 获取 appCode
  int _getAppCode() {
    return Platform.isAndroid ? 162 : 163;
  }

  /// 短信登录
  Future<UserLoginModel?> smsLogin(String mobile, String smsCode) async {
    var params = {
      "appCode": _getAppCode(),
      "smsCode": smsCode,
      "mobile": mobile,
    };

    ResultData resultData = await httpManager.requestUrl(
      "/xtjLogin/smsLogin",
      method: HttpMethod.POST,
      params: params,
    );

    // 如果返回的 code 是 400，显示验证码错误
    if (Code.equals(resultData.code, 400)) {
      EasyLoading.showToast('验证码错误');
      return null; // 处理错误，不返回数据
    }

    // 使用 Code.equals() 进行比较，成功时返回数据
    if (Code.equals(resultData.code, Code.SUCCESS) && resultData.data != null) {
      UserLoginModel data =
          UserLoginModel.fromJson(resultData.data as Map<String, dynamic>);
      AppInfo.getInstance()?.setIsLogin = true;
      return data; // 登录成功，返回数据
    } else {
      // 其他错误
      String? msg = resultData.msg;
      if (msg.isNotEmpty) {
        EasyLoading.showToast(msg);
      }
      return null; // 返回 null 表示登录失败
    }
  }

  /// 移动认证
  Future<UserLoginModel?> identifyMobile(String mobileToken) async {
    var params = {
      "appCode": _getAppCode(),
      "mobileToken": mobileToken,
    };

    ResultData resultData = await httpManager.requestUrl(
      "/xtjLogin/identifyMobile",
      method: HttpMethod.POST,
      params: params,
    );

    if (resultData.code == Code.SUCCESS && resultData.data != null) {
      // Parse the response using SmsLoginResponse model
      UserLoginModel data =
          UserLoginModel.fromJson(resultData.data as Map<String, dynamic>);

      // Handle the parsed response data here (e.g., save token or update UI)
      AppInfo.getInstance()?.setIsLogin = true;

      return data;
    } else {
      // Handle error message if any
      String? msg = resultData.msg;
      if (msg.isNotEmpty) {
        EasyLoading.showToast(msg);
      }
      return null;
    }
  }

  /// 获取小程序码
  Future<ResultData?> getMiniProgramCode() async {
    int appCode = _getAppCode();
    bool isInstalled = await WeChatService().checkIfWeChatInstalled();

    Map<String, dynamic> params = {
      "app_code": appCode,
    };

    // 如果微信没有安装，传入code_img参数
    if (!isInstalled) {
      params["code_img"] = 1;
    }
    // params["code_img"] = 1;

    ResultData resultData = await httpManager.requestUrl(
      "/xtjLogin/miniLogin/genLoginId",
      params: params,
      method: HttpMethod.GET,
    );

    return resultData;
  }

  /// 检查是否扫码
  Future<ResultData?> checkMiniProgramLogin(String loginId) async {
    ResultData resultData = await httpManager.requestUrl(
      "/xtjLogin/miniLogin/checkLogin",
      params: {
        "login_id": loginId,
      },
      method: HttpMethod.GET,
    );

    return resultData;
  }

  /// 查看旧数据
  Future<ResultData?> getDistributionSyetemInfo(String guid) async {
    ResultData resultData = await httpManager.requestUrl(
        "/distribution_system/api/person/info",
        params: {'guid': guid},
        method: HttpMethod.GET);
    return resultData;
  }

  /// 查看个人信息 code 1-未注册 2-已注册
  Future<ResultData?> getUserInfo() async {
    ResultData resultData = await httpManager
        .requestUrl("/xtj-people/app/user/info", method: HttpMethod.GET);
    return resultData;
  }

  void getUserInfoWithCache({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/user/info';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  /// 查看嘉宾信息
  Future<ResultData?> getGuestInfo() async {
    ResultData resultData = await httpManager
        .requestUrl("/xtj-people/app/guest/login", method: HttpMethod.GET);
    return resultData;
  }

  /// 获取部门列表
  Future<ResultData?> getDepartmentList() async {
    ResultData resultData = await httpManager
        .requestUrl("/xtj-people/app/department/list", method: HttpMethod.GET);
    return resultData;
  }

  ///老用户老部门换新部门
  Future<ResultData?> oldDepartToNewDepart(String be_did) async {
    ResultData resultData = await httpManager.requestUrl(
        "/xtj-people/app/department/mp_map",
        params: {'department_id': be_did},
        method: HttpMethod.GET);
    return resultData;
  }

  /// 获取手机号
  Future<ResultData?> getPhoneNumberRegister() async {
    ResultData resultData = await httpManager.requestUrl("/xtjUser/myMobile",
        method: HttpMethod.POST);
    return resultData;
  }

  /// 获取岗位列表
  Future<ResultData?> getRoleList(int departmentId) async {
    ResultData resultData = await httpManager.requestUrl(
        "/xtj-people/app/role/list_by_department",
        params: {'department_id': departmentId},
        method: HttpMethod.GET);
    return resultData;
  }

  /// 获取部门内审核人列表
  Future<ResultData?> getReviewerList(int departmentId) async {
    ResultData resultData = await httpManager.requestUrl(
        "/xtj-people/app/user/approval/reviewer",
        params: {'department_id': departmentId},
        method: HttpMethod.GET);
    return resultData;
  }

  /// 获取自己审核人
  Future<ResultData?> getMyReviewer() async {
    ResultData resultData = await httpManager.requestUrl(
        "/xtj-people/app/user/approval/myself",
        method: HttpMethod.GET);
    return resultData;
  }

  /// 提交注册
  Future<ResultData?> commitRegister(Map params) async {
    ResultData resultData = await httpManager.requestUrl(
        "/xtj-people/app/user/register",
        params: params,
        method: HttpMethod.POST);
    return resultData;
  }

  ///老用户完善信息
  Future<ResultData?> oldCommitRegister(Map params) async {
    ResultData resultData = await httpManager.requestUrl(
        "/xtj-people/app/user/register_old_system",
        params: params,
        method: HttpMethod.POST);
    return resultData;
  }

  /// 我的审批列表
  Future<ResultData?> getApprovalList(String state,
      {int? page = 1, int size = 10, String? status1, String? status2}) async {
    /// state REVIEWING REVIEW_PASS REVIEW_REJECT

    Map<String, dynamic>? params;
    params = {'page': page, 'size': size, 'status': state};
    if (status1 != null && status2 != null) {
      params = {
        'page': page,
        'size': size,
        'status': Uri.parse('$state&status=$status1&status=$status2'),
      };
    } else if (status1 != null) {
      params = {
        'page': page,
        'size': size,
        'status': Uri.parse('$state&status=$status1')
      };
    } else if (status2 != null) {
      params = {
        'page': page,
        'size': size,
        'status': Uri.parse('$state&status=$status2')
      };
    }

    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/user/approval/list',
        params: params,
        method: HttpMethod.GET);
    return resultData;
  }

  Future<ResultData?> approvalWithType(int id, String type,
      {String? note}) async {
    Map<String, dynamic>? params;
    if (type == 'REVIEW_PASS') {
      params = {'review_id': id, 'status': type};
    } else {
      //REVIEW_REJECT
      params = {'review_id': id, 'status': type, 'note': note};
    }

    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/user/approval/set',
        params: params,
        method: HttpMethod.POST);
    return resultData;
  }

  Future<ResultData?> uploadAvatar(String path) async {
    String fileName = path.split("/").last;
    FormData formData = FormData.fromMap(
        {"file": await MultipartFile.fromFile(path, filename: fileName)});

    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/user/upload_avatar',
        params: formData,
        method: HttpMethod.POST);
    return resultData;
  }

  Future<ResultData?> setAvatar(String path) async {
    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/user/set_avatar',
        params: {'avatar': path},
        method: HttpMethod.POST);
    return resultData;
  }

  //查验用户权限
  Future<bool> checkUserPermission(int menu_id) async {
    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/xiaoxin_school/check',
        params: {'menu_id': menu_id},
        method: HttpMethod.GET);
    if (resultData.data == null || resultData.code != 0) {
      EasyLoading.showInfo('没有权限，请联系管理员');
      return false;
    } else {
      if (resultData.data) {
        return true;
      } else {
        EasyLoading.showInfo('没有权限，请联系管理员');
        return false;
      }
    }
  }

  //查验用户权限
  Future<bool> checkFunctionUserPermission(int menu_id) async {
    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/utility/check',
        params: {'menu_id': menu_id},
        method: HttpMethod.GET);
    if (resultData.data == null || resultData.code != 0) {
      EasyLoading.showInfo('没有权限，请联系管理员');
      return false;
    } else {
      if (resultData.data) {
        return true;
      } else {
        EasyLoading.showInfo('没有权限，请联系管理员');
        return false;
      }
    }
  }

  //小新学院菜单
  Future<void> getXiaoxinSchoolMenuList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) async {
    String url = '/xtj-people/app/xiaoxin_school/list';
    httpManager.requestUrlWithCache(url, url, (cache) {
      if (cacheCallBack != null) {
        cacheCallBack(cache);
      }
    }, (success) {
      if (successCallBack != null) {
        successCallBack(success);
      }
    }, (error) {
      if (errorCallBack != null) {
        errorCallBack(error);
      }
    }, method: HttpMethod.GET);

    // ResultData resultData = await httpManager.requestUrl(
    //     '/xtj-people/app/xiaoxin_school/list',
    //     method: HttpMethod.GET);
    // return resultData;
  }

  //功能菜单
  Future<void> getFunctionlMenuList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) async {
    String url = '/xtj-people/app/utility/list';
    httpManager.requestUrlWithCache(url, url, (cache) {
      if (cacheCallBack != null) {
        cacheCallBack(cache);
      }
    }, (success) {
      if (successCallBack != null) {
        successCallBack(success);
      }
    }, (error) {
      if (errorCallBack != null) {
        errorCallBack(error);
      }
    }, method: HttpMethod.GET);
  }

  //子用户功能菜单
  Future<void> getSublMenuList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) async {
    String url = '/xtj-people/app/user_sub/utilities';
    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  //子用户功能菜单
  Future<ResultData?> getSubMenuListAsync() async {
    ResultData? result = await httpManager.requestUrl(
        '/xtj-people/app/user_sub/utilities',
        method: HttpMethod.GET);
    return result;
  }

  //子账户解除绑定
  Future<ResultData?> subUserDeleteRelation() async {
    ResultData resultData = await httpManager.requestUrl(
        "/xtj-people/app/user_sub/delete_relation",
        method: HttpMethod.GET);
    return resultData;
  }

  //获取课程排名
  Future<void> getCourseRankList({
    int? courseId,
    String? start,
    String? end,
    int page = 1,
    int size = 10,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) async {
    Map<String, dynamic>? params = {};
    if (courseId != null) {
      params['course_ids'] = courseId;
    }
    if (start != null) {
      params['start'] = start;
    }
    if (end != null) {
      params['end'] = end;
    }
    params['page'] = page;
    params['size'] = size;
    String url = '/xtj-people/app/course/watch/rank/course/list';

    httpManager.requestUrlWithCache(url, url, (cache) {
      if (cacheCallBack != null) {
        cacheCallBack(cache);
      }
    }, (success) {
      if (successCallBack != null) {
        successCallBack(success);
      }
    }, (error) {
      if (errorCallBack != null) {
        errorCallBack(error);
      }
    }, params: params, method: HttpMethod.GET);

    // ResultData resultData = await httpManager.requestUrl(
    //     '/xtj-people/app/course/watch/rank/course/list',
    //     params: params,
    //     method: HttpMethod.GET);
    // return resultData;
  }

  //获取部门排名
  Future<void> getDepartRankList({
    int? departId,
    String? start,
    String? end,
    int page = 1,
    int size = 10,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) async {
    Map<String, dynamic>? params = {};
    if (departId != null) {
      params['department_id'] = departId;
    }
    if (start != null) {
      params['start'] = start;
    }
    if (end != null) {
      params['end'] = end;
    }
    params['page'] = page;
    params['size'] = size;
    String url = '/xtj-people/app/course/watch/rank/depart/list';

    httpManager.requestUrlWithCache(url, url, (cache) {
      if (cacheCallBack != null) {
        cacheCallBack(cache);
      }
    }, (success) {
      if (successCallBack != null) {
        successCallBack(success);
      }
    }, (error) {
      if (errorCallBack != null) {
        errorCallBack(error);
      }
    }, params: params, method: HttpMethod.GET);

    // ResultData resultData = await httpManager.requestUrl(
    //     '/xtj-people/app/course/watch/rank/depart/list',
    //     params: params,
    //     method: HttpMethod.GET);
    // return resultData;
  }

  //获取健康pk列表
  void getHealthPKList(
    int year, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/health/list';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        params: {'year': year}, method: HttpMethod.GET);
  }

  //获取健康步数榜
  void getHealthWalkRankList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/health/walk/list';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  // 获取学习tag id 1-能力提升tag
  void getCourseTagList(
    int id, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/course/tag/list';

    httpManager.requestUrlWithCache(url, url, params: {'id': id}, (cache) {
      if (cacheCallBack != null) {
        cacheCallBack(cache);
      }
    }, (success) {
      if (successCallBack != null) {
        successCallBack(success);
      }
    }, (error) {
      if (errorCallBack != null) {
        errorCallBack(error);
      }
    }, method: HttpMethod.GET);
  }

  // 获取校区培训tag
  void getSchoolTagList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/course/tag/school/list';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  // 获取课程列表
  void getCourseList(
    int tag_id, {
    String? keyword,
    int? page = 1,
    int? size = 10,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};
    params['tag_id'] = tag_id;
    if (keyword!.isNotEmpty) {
      params['keyword'] = keyword;
    }
    params['page'] = page;
    params['size'] = size;

    String url = '/xtj-people/app/course/list';
    String cacheId = '$url/&tagId=$tag_id&page=$page&size=$size';

    httpManager.requestUrlWithCache(url, cacheId, (cache) {
      if (cacheCallBack != null) {
        cacheCallBack(cache);
      }
    }, (success) {
      if (successCallBack != null) {
        successCallBack(success);
      }
    }, (error) {
      if (errorCallBack != null) {
        errorCallBack(error);
      }
    }, params: params, method: HttpMethod.GET);
  }

  ///获取课程详情
  void getCourseDetail(
    int courseId, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/course/info';
    String cacheId = '$url/$courseId';

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: {'course_id': courseId}, method: HttpMethod.GET);
  }

  Future<ResultData> getCourseDetailAsync(int courseId) async {
    Map<String, dynamic> params = {"course_id": courseId};

    ResultData resultData = await httpManager.requestUrl(
      "/xtj-people/app/course/info",
      params: params,
      method: HttpMethod.GET,
    );

    return resultData;
  }

  //获取年会tag
  void getAnnualMeetingVideoTags({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/course/tag/meeting/list';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  // 获取年会视频列表
  void getAnnualMeetingVideoList({
    String? keyword,
    int? tag_id,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};
    if (keyword != null) {
      params['keyword'] = keyword;
    }
    if (tag_id != null) {
      params['tag_id'] = tag_id;
    }

    String url = '/xtj-people/app/course/meeting/list';
    String cacheId = '$url/$tag_id';

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  // 获取校区培训课程列表
  void getSchoolCourseList({
    int? tag_id,
    String? keyword,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};
    params['tag_id'] = tag_id;
    if (keyword != null) {
      params['keyword'] = keyword;
    }

    String url = '/xtj-people/app/course/school/list';
    String cacheId = '${url}/$tag_id';

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  //获取课程章节
  void getCourseChapter(
    int course_id, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/course/chapter';
    String cacheId = '$url/$course_id';

    httpManager.requestUrlWithCache(url, cacheId, (cache) {
      if (cacheCallBack != null) {
        cacheCallBack(cache);
      }
    }, (success) {
      if (successCallBack != null) {
        successCallBack(success);
      }
    }, (error) {
      if (errorCallBack != null) {
        errorCallBack(error);
      }
    }, params: {'course_id': course_id}, method: HttpMethod.GET);
  }

  //获取课程章节观看记录
  void getCourseWatchList({
    int? course_id,
    int? chapter_id,
    int? lesson_id,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};

    if (course_id != null) {
      params['course_id'] = course_id;
    }
    if (chapter_id != null) {
      params['chapter_id'] = chapter_id;
    }
    if (lesson_id != null) {
      params['lesson_id'] = lesson_id;
    }

    String url = '/xtj-people/app/course/watch/course/list';
    String cacheId = '$url/${params.toString()}';

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  Future<ResultData?> getCourseWatchListAsync(
      {int? course_id, int? chapter_id, int? lesson_id}) async {
    Map<String, dynamic>? params = {};

    if (course_id != null) {
      params['course_id'] = course_id;
    }
    if (chapter_id != null) {
      params['chapter_id'] = chapter_id;
    }
    if (lesson_id != null) {
      params['lesson_id'] = lesson_id;
    }

    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/course/watch/course/list',
      params: params,
      method: HttpMethod.GET,
    );

    return resultData;
  }

  //获取正在学习列表,看课历史
  void getWatchingList({
    int? tag_id,
    int? page,
    int? size,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};
    if (tag_id != null) {
      params['tag_id'] = tag_id;
      params['page'] = page;
      params['size'] = size;
    }

    String url = '/xtj-people/app/course/watch/list';
    String cacheId = '$url/$tag_id';

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  //获取我的收藏列表
  void getMyStarList({
    String? keyword,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};
    if (keyword != null) {
      params['keyword'] = keyword;
    }

    String url = '/xtj-people/app/course/star/list';

    httpManager.requestUrlWithCache(
      url,
      url,
      cacheCallBack,
      successCallBack,
      errorCallBack,
      params: params,
      method: HttpMethod.GET,
    );
  }

  //正在看课列表 //数据有2分钟时效性,过期不再返回
  void getCourseWatchingList(
    int course_id, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/course/watch/study/list';

    httpManager.requestUrlWithCache(
      url,
      url,
      cacheCallBack,
      successCallBack,
      errorCallBack,
      params: {'course_id': course_id},
      method: HttpMethod.GET,
    );
  }

  //获取课程资料
  void getCourseHandout({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/course/handout';

    httpManager.requestUrlWithCache(
      url,
      url,
      cacheCallBack,
      successCallBack,
      errorCallBack,
      method: HttpMethod.GET,
    );
  }

  //会议ppt tag
  void getPptMeetingTagList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/ppt/meeting/tag/list';

    httpManager.requestUrlWithCache(
      url,
      url,
      cacheCallBack,
      successCallBack,
      errorCallBack,
      method: HttpMethod.GET,
    );
  }

  //会议ppts
  void getPptMeetingList({
    int? tag_id,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};
    if (tag_id != null) {
      params['tag_id'] = tag_id;
    }

    String url = '/xtj-people/app/ppt/meeting/list';
    String cacheId = '$url/$tag_id';

    httpManager.requestUrlWithCache(
      url,
      cacheId,
      cacheCallBack,
      successCallBack,
      errorCallBack,
      params: params,
      method: HttpMethod.GET,
    );
  }

  //获取运营工具列表
  void getOperationalToolList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/tools/list';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  // 获取试卷分类
  void getPaperCategoryList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/exam/paper_category/list';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  // 获取试卷列表
  void getPaperList({
    int? paper_category_id,
    int? page,
    int? size,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};
    if (paper_category_id != null) {
      params['paper_category_id'] = paper_category_id;
    }
    if (page != null) {
      params['page'] = page;
    }
    if (size != null) {
      params['size'] = size;
    }

    String url = '/xtj-people/app/exam/paper/list';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  // 获取试卷数据
  void getPaperData(
    int paper_id, {
    int? missionId,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic> params = {};
    params['paper_id'] = paper_id;
    if (missionId != null) {
      params['mission_id'] = missionId;
    }

    String url = '/xtj-people/app/exam/paper_data';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  // 获取试卷扩展数据
  void getPaperExtendData(
    int paper_id,
    int? missionId, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic> params = {};
    params['paper_id'] = paper_id;
    params['mission_id'] = missionId;

    String url = '/xtj-people/app/exam/paper_extra_data';

    httpManager.requestUrlWithCache(
        url, url, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  // 获取试卷内容
  //question_id cache 用
  void getPaperContent(
    int paper_id, {
    int? missionId,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic> params = {};
    params['paper_id'] = paper_id;
    if (missionId != null) {
      params['mission_id'] = missionId;
    }

    String url = '/xtj-people/app/exam/paper_content';
    String cacheId = '$url/$paper_id';

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  //获取试卷排行
  Future<ResultData?> getPaperRank(int paper_id,
      {int page = 1,
      int page_size = 10,
      int? department_id,
      int? mission_id}) async {
    Map<String, dynamic> params = {};
    if (department_id != null) {
      params['department_id'] = department_id;
    }
    if (mission_id != null) {
      params['mission_id'] = mission_id;
    }
    params['paper_id'] = paper_id;
    params['page'] = page;
    params['page_size'] = page_size;

    ResultData? res = await httpManager.requestUrl(
      '/xtj-people/app/exam/paper_rank',
      params: params,
      method: HttpMethod.GET,
    );
    return res;
  }

  //团队管理 - 获取用户部门列表
  void getTeamManagerDetartments({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/department/owner';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

//团队管理 - 获取指定部门成员列表
  void getTeamManagerPersons(
    int department_id,
    int page,
    int size, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/department/members/list';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: {
          'department_id': department_id,
          'page': page,
          'size': size,
        },
        method: HttpMethod.GET);
  }

  //团队管理 - 编辑
  Future<ResultData?> teamManagerEdit(
    int department_id,
    String user_guid,
    String user_name,
    List role_ids,
    String status, // normal 正常，forzen 冻结
    int promotion_department_id, // 增长部门id 0 禁用推广， 其他为推广
    int distribution_department_id, //分销部门id
  ) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/department/members/edit?department_id=$department_id',
      params: {
        'user_guid': user_guid,
        'user_name': user_name,
        'role_ids': role_ids,
        'status': status,
        'promotion_department_id': promotion_department_id,
        'distribution_department_id': distribution_department_id,
      },
      method: HttpMethod.POST,
    );

    return resultData;
  }

  //团队管理 - 修改部门介绍
  Future<ResultData?> teamManagerDepartMembersEdit(
      int department_id, String about) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/department/edit?department_id=$department_id',
      params: {'about': about},
      method: HttpMethod.POST,
    );

    return resultData;
  }

  //新途径圈 - 获取圈子分类
  void getCircleCategory({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/circle/category/list';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  //新途径圈 - 获取推文
  void getCirclePosts({
    int? category_id,
    int? page,
    int? size,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/circle/posts/list';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: {
          'category_id': category_id,
          "page": page,
          "size": size,
        },
        method: HttpMethod.GET);
  }

  //新途径圈 - 点赞/取消点赞
  void getCirclePostLike(
    int post_id, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/circle/posts/like';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: {'post_id': post_id}, method: HttpMethod.GET);
  }

  //新途径圈 - banner-列表
  void getBannerList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/circle/banner/list';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  //新途径圈 - 首页通知
  void getCircleNotifyList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/circle/notify/list';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  //新途径圈 - 首页悬浮弹窗
  void getCirclePopList({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/circle/pop/list';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  //我的任务 - 任务列表
  void getMissionList({
    List? status,
    int? page,
    int? size,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/mission/list';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: {'page': page, 'size': size, 'status': status},
        method: HttpMethod.GET);
  }

  //获取任务待办
  Future<ResultData?> getMissionListAsync(List status) async {
    ResultData? res = await httpManager.requestUrl(
      '/xtj-people/app/mission/list',
      params: {'status': status},
      method: HttpMethod.GET,
    );

    return res;
  }

  //我的任务 - 任务列表
  void getSubMissionList(
    int missionUserId, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/mission/sub/list';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: {'mission_user_id': missionUserId}, method: HttpMethod.GET);
  }

  //我的任务 - 存在任务用户的部门列表
  void getMissionDepartments(
    int missionId, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/mission/rank/course/departments';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: {'mission_id': missionId}, method: HttpMethod.GET);
  }

  //我的任务 - 部门榜
  void getMissionDepartmentsRank(
    int missionId, {
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/mission/rank/department';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: {'mission_id': missionId}, method: HttpMethod.GET);
  }

  //我的任务 - 部门榜
  void getMissionCourseRank(
    int missionId, {
    int? departmentId, //部门ID，从接口 【获取存在任务用户部门列表】 获取，不存在或为0为不筛选部门，看总榜
    bool? orderByTotal = true, //是否按总数排序，是的话按总数排序，否的话按当日排序
    int? page,
    int? size,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    Map<String, dynamic>? params = {};
    params['mission_id'] = missionId;
    if (departmentId != null) {
      params['department_id'] = departmentId;
    }
    params['order_by_total'] = orderByTotal;
    if (page != null) {
      params['page'] = page;
    }
    if (size != null) {
      params['size'] = size;
    }

    String url = '/xtj-people/app/mission/rank/course';
    String cacheId = url;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        params: params, method: HttpMethod.GET);
  }

  //提交答案
  Future<ResultData?> submitAnswer(
    int answer_record_id,
    int question_id,
    int paper_id,
    List answer_list,
    int remain_time,
  ) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/exam/submit_answer',
      params: {
        'answer_record_id': answer_record_id,
        'question_id': question_id,
        'paper_id': paper_id,
        'answer_list': answer_list,
        'remain_time': remain_time,
      },
      method: HttpMethod.POST,
    );

    return resultData;
  }

  // 上传答题消耗时间
  Future<ResultData?> submitExamConsumeTime(
      int answer_record_id, int consume_time) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/exam/consume_time',
      params: {
        'answer_record_id': answer_record_id,
        'consume_time': consume_time
      },
      method: HttpMethod.POST,
    );

    return resultData;
  }

  // 腾讯IM -- 获取登录IM需要的信息
  Future<ResultData?> getIMLoginInfo() async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/tim/get_user_sig',
      method: HttpMethod.GET,
    );

    return resultData;
  }

  // 通讯录
  void getContacts({
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack,
  }) {
    String url = '/xtj-people/app/contact/departments';
    String cacheId = LocalCachekeys.contactsKey;

    httpManager.requestUrlWithCache(
        url, cacheId, cacheCallBack, successCallBack, errorCallBack,
        method: HttpMethod.GET);
  }

  // 通讯录 -- 获取手机号码
  Future<ResultData?> getPhoneNumber(String user_guid) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/contact/mobile',
      params: {'user_guid': user_guid},
      method: HttpMethod.GET,
    );

    return resultData;
  }

  // 收藏、取消课程
  Future<ResultData?> starOrCancelCourse(int course_id) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/course/star/save',
      params: {
        'course_id': course_id,
        'user_id': GlobalPreferences().userInfo?.user.id,
      },
      method: HttpMethod.POST,
    );

    return resultData;
  }

  // 添加看课记录 退出页面，进入后台，切换课程时调用
  Future<ResultData?> addCourseWatchSave(
      int chapter_id, int course_id, int duration, int lesson_id) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/course/watch/save',
      params: {
        'chapter_id': chapter_id,
        'course_id': course_id,
        'duration': duration,
        'lesson_id': lesson_id,
      },
      method: HttpMethod.POST,
    );

    return resultData;
  }

  //初始化看课时长 刚开始播放的时候调用
  Future<ResultData?> initCourseWatchSaveDate(int course_id) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/course/watch/study/add',
      params: {'course_id': course_id},
      method: HttpMethod.POST,
    );

    return resultData;
  }

  // 添加看课时长 每分钟调用一次
  Future<ResultData?> addCourseWatchSaveDate(
      int chapter_id, int course_id, int duration, int lesson_id) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/course/watch/save/date',
      params: {
        'chapter_id': chapter_id,
        'course_id': course_id,
        'duration': duration,
        'lesson_id': lesson_id,
      },
      method: HttpMethod.POST,
    );

    return resultData;
  }

  //健康pk 上传体重图片
  Future<ResultData?> uploadWeightImg(String path) async {
    String fileName = path.split("/").last;
    FormData formData = FormData.fromMap(
        {"file": await MultipartFile.fromFile(path, filename: fileName)});

    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/health/upload/weight',
        params: formData,
        method: HttpMethod.POST);
    return resultData;
  }

  //健康pk 上传体重
  Future<ResultData?> addHealthWeight(
      int id, int health_id, double weight, String img) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/health/save',
      params: {'id': id, 'health_id': health_id, 'weight': weight, 'img': img},
      method: HttpMethod.POST,
    );

    return resultData;
  }

  //健康pk 上传步数
  Future<ResultData?> addHealthStep(int num) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-people/app/health/walk/save',
      params: {'walk_num': num},
      method: HttpMethod.POST,
    );

    return resultData;
  }

  //获取版本更新信息
  Future<ResultData?> getVersionInfo() async {
    PackageInfo info = await PackageInfo.fromPlatform();
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-config/app/getVersion',
      params: {
        'app_code': Platform.isAndroid ? 162 : 163,
        'version': info.version,
      },
      method: HttpMethod.GET,
    );

    return resultData;
  }

  //获取新版本商店信息
  Future<ResultData?> getStoreInfo(String version, String storeName) async {
    ResultData resultData = await httpManager.requestUrl(
      '/xtj-config/app/queryAppStatus',
      params: {
        'appCode': Platform.isAndroid ? 162 : 163,
        'version': version,
        'storeName': storeName,
      },
      method: HttpMethod.GET,
    );
    return resultData;
  }

  //查询用户看课
  Future<ResultData?> queryUserWatch(
      String number, String start, String end) async {
    ResultData resultData =
        await httpManager.requestUrl('/school_target_status/queryUserWatch',
            params: {
              'phone': number,
              'fromDate': start,
              'toDate': end,
            },
            method: HttpMethod.POST);
    return resultData;
  }

  //设置入职时间
  Future<ResultData?> setJoinAt(String join_at) async {
    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/user/set_join_at',
        params: {'join_at': join_at},
        method: HttpMethod.POST);
    return resultData;
  }

  //上传我的二维码
  Future<ResultData?> uploadMyCode(List<String> codes) async {
    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/user/set_wechat_qrcode',
        params: {'wechat_qrcode': codes},
        method: HttpMethod.POST);
    return resultData;
  }

  //根据ID查询部门信息
  Future<ResultData?> getDepartInfoWithId(int id) async {
    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/department/info',
        params: {'id': id},
        method: HttpMethod.GET);
    return resultData;
  }

  //查询是否可以催促
  Future<ResultData?> getApprovalCanPing() async {
    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/user/approval/can_ping',
        method: HttpMethod.GET);
    return resultData;
  }

  //催促审核
  Future<ResultData?> getApprovalPing() async {
    ResultData resultData = await httpManager.requestUrl(
        '/xtj-people/app/user/approval/ping',
        method: HttpMethod.GET);
    return resultData;
  }
}
