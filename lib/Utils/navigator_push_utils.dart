import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/model/home/<USER>';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:url_launcher/url_launcher.dart';

class NavigatorPushUtils {
  //自定义跳转页面
  static Future<void> to(BuildContext context, List? jump, List? style,
      void Function()? callback) async {
    Map? jumpMap = {};
    Map? styleMap = {};
    for (var element in jump ?? []) {
      if (element is BannerJumpModel) {
        if (element.key == 'flutter') {
          jumpMap?['key'] = element.key;
          jumpMap?['value'] = element.value;
        }
      } else if (element is Map) {
        if (element['key'] == 'flutter') {
          jumpMap = element;
        } else {
          debugPrint('---------- 自定义跳转类型错误');
        }
      }
    }

    for (var element in style ?? []) {
      if (element['key'] == 'flutter') {
        styleMap = jsonDecode(element['value']);
      }
    }

    if (jumpMap != null) {
      String valueStr = jumpMap['value'];
      String type = valueStr.split("@").first;
      String title = valueStr.split("@")[1];
      String path = valueStr.split("@").last;
      if (type == 'h5') {
        if (title == "海报换码") {
          NavigatorUtils.push(context, CommonRouter.posterChangeCodeWebView,
              arguments: {'url': path, 'title': title}).then((v) {
            if (callback != null) {
              callback();
            }
          });
        } else {
          NavigatorUtils.push(context, CommonRouter.webview, arguments: {
            'url': path,
            'title': title,
            'need_landscape': styleMap?['need_landscape'],
            'need_nav': styleMap?['need_nav'],
            'need_back_btn': styleMap?['need_back_btn'],
            'enable_long_press': title == '增长码' ? true : false,
            'keep_alive': false,
          }).then((v) {
            if (callback != null) {
              callback();
            }
          });
        }
      } else if (type == 'page') {
        String version = styleMap?['version'] ?? '';
        if (version.isNotEmpty) {
          String localVersion = await AppInfo().getFlutterAppVersion();
          if (getVersion(localVersion) < getVersion(version)) {
            showDialog(
                barrierDismissible: false,
                context: context,
                builder: (_) {
                  return const PageDialog();
                });
            return;
          }
        }
        NavigatorUtils.push(context, path, arguments: {'title': title})
            .then((v) {
          if (callback != null) {
            callback();
          }
        });
      } else if (type == 'pdf') {
        NavigatorUtils.push(context, CommonRouter.pdfPreviewPage,
            arguments: {'filePath': path, 'title': title}).then((v) {
          if (callback != null) {
            callback();
          }
        });
      } else if (type == 'wx') {
        String wxId = valueStr.split("@")[2];
        String wxPath = valueStr.split("@")[3];
        WeChatService().launchWeChatMiniProgram(userName: wxId, path: wxPath);
      } else {
        debugPrint(' ------ 未定义页面');
      }
    }
  }

  static Future<void> toWithPath(
      BuildContext context, String patha, void Function()? callback) async {
    String valueStr = patha;
    String type = valueStr.split("@").first;
    String title = valueStr.split("@")[1];
    String path = valueStr.split("@").last;
    if (type == 'h5') {
      NavigatorUtils.push(context, CommonRouter.webview, arguments: {
        'url': path,
        'title': title,
        'enable_long_press': title == '增长码' ? true : false,
        'keep_alive': false,
      }).then((v) {
        if (callback != null) {
          callback();
        }
      });
    } else if (type == 'page') {
      NavigatorUtils.push(context, path, arguments: {'title': title}).then((v) {
        if (callback != null) {
          callback();
        }
      });
    } else if (type == 'pdf') {
      NavigatorUtils.push(context, CommonRouter.pdfPreviewPage,
          arguments: {'filePath': path, 'title': title}).then((v) {
        if (callback != null) {
          callback();
        }
      });
    } else if (type == 'wx') {
      String wxId = valueStr.split("@")[2];
      String wxPath = valueStr.split("@")[3];
      WeChatService().launchWeChatMiniProgram(userName: wxId, path: wxPath);
    } else {
      debugPrint(' ------ 未定义页面');
    }
  }

  static int getVersion(String version) {
    int result;
    version = version.replaceAll('.', '');
    result = int.parse(version);
    return result;
  }
}

class PageDialog extends StatelessWidget {
  const PageDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: ScreenUtil().screenWidth - 52.w,
        margin: EdgeInsetsDirectional.symmetric(horizontal: 26.w),
        padding:
            EdgeInsetsDirectional.symmetric(vertical: 22.h, horizontal: 28.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('提示',
                style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF333333))),
            SizedBox(height: 19.h),
            Text(
              '当前版本暂无法使用此功能，请先更新APP。',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 15.sp, color: const Color(0xFF333333)),
            ),
            SizedBox(height: 25.h),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      height: 44.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: const Color(0xFFECECEC),
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Text(
                        '取消',
                        style: TextStyle(
                          color: const Color(0xFFB3B3B3),
                          fontSize: 15.sp,
                        ).pfMedium,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _handleConfirm(context),
                    child: Container(
                      height: 44.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        color: const Color(0xFF0054FF),
                      ),
                      child: Text(
                        '去升级',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 15.sp,
                        ).pfMedium,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleConfirm(BuildContext context) async {
    Navigator.pop(context);
    if (Platform.isIOS) {
      Uri appStoreUrl = Uri.parse('https://apps.apple.com/app/id6503936061');
      if (await canLaunchUrl(appStoreUrl)) {
        launchUrl(appStoreUrl, mode: LaunchMode.externalApplication);
      }
    } else if (Platform.isAndroid) {
      launchAppMarket('com.xtj.person');
    }
  }

  Future<void> launchAppMarket(String packageName) async {
    final List<String> marketUrls = [
      "market://details?id=$packageName", // 通用方式
      "https://a.app.qq.com/o/simple.jsp?pkgname=$packageName", // 应用宝
      "appmarket://details?id=$packageName", // 华为
      "mimarket://details?id=$packageName", // 小米
      "vivomarket://details?id=$packageName", // VIVO
      "market://details?id=$packageName", // OPPO
    ];

    for (String url in marketUrls) {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        return;
      }
    }

    String fallbackUrl = "https://m.xtjzx.cn/app/xtjr-app-download-page/";
    await launchUrl(Uri.parse(fallbackUrl),
        mode: LaunchMode.externalApplication);
  }
}
