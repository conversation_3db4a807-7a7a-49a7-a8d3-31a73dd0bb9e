import 'dart:math';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:path_provider/path_provider.dart';

enum DeviceType { Desktop, Mobile }

class FormFactor {
  static double desktop = 900;
  static double handset = 300;
}

class ValidatorUtils {
  /// Validate if the input is a valid phone number
  static bool isPhoneNumber(String input) {
    // Simple regex for Chinese phone numbers
    final RegExp phoneRegex = RegExp(r"^1[3-9]\d{9}$");
    return phoneRegex.hasMatch(input);
  }

  /// Validate if the input is a valid email
  static bool isEmail(String input) {
    final RegExp emailRegex =
        RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
    return emailRegex.hasMatch(input);
  }

  // 校验身份证号码
  static bool validate(String idCard) {
    if (idCard.length != 18) {
      return false; // 长度不为18位
    }

    // 验证正则表达式（18位）
    final regExp = RegExp(r'^\d{17}(\d|X|x)$');
    if (!regExp.hasMatch(idCard)) {
      return false; // 格式不正确
    }

    // 校验出生日期
    if (!validateBirthDate(idCard.substring(6, 14))) {
      return false; // 出生日期不合法
    }

    // 校验校验码
    if (!validateCheckCode(idCard)) {
      return false; // 校验码不正确
    }

    return true; // 如果所有检查都通过
  }

  // 验证出生日期
  static bool validateBirthDate(String birthDate) {
    final year = int.tryParse(birthDate.substring(0, 4));
    final month = int.tryParse(birthDate.substring(4, 6));
    final day = int.tryParse(birthDate.substring(6, 8));

    if (year == null || month == null || day == null) {
      return false; // 如果解析出生年月日失败
    }

    final date = DateTime(year, month, day);
    return date.year == year && date.month == month && date.day == day;
  }

  // 校验校验码
  static bool validateCheckCode(String idCard) {
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; // 权重
    const checkCodes = [
      '1',
      '0',
      'X',
      '9',
      '8',
      '7',
      '6',
      '5',
      '4',
      '3',
      '2'
    ]; // 校验码

    int sum = 0;
    for (int i = 0; i < 17; i++) {
      sum += int.parse(idCard[i]) * weights[i];
    }

    final checkCode = checkCodes[sum % 11]; // 计算出的校验码
    return checkCode == idCard[17].toUpperCase(); // 校验输入的校验码是否匹配
  }

  static String formatDuration(int seconds) {
    final int hours = seconds ~/ 3600;
    final int minutes = (seconds % 3600) ~/ 60;
    final int secs = seconds % 60;

    if (hours > 0) {
      // 格式化为 hh:mm:ss
      return '${_twoDigits(hours)}:${_twoDigits(minutes)}:${_twoDigits(secs)}';
    } else if (minutes > 0) {
      // 格式化为 mm:ss
      return '${_twoDigits(minutes)}:${_twoDigits(secs)}';
    } else {
      // 格式化为 ss
      return '00:${_twoDigits(secs)}';
    }
  }

  static String _twoDigits(int n) => n.toString().padLeft(2, '0');

  static String testImageUrl = 'https://avatar.xtjzx.cn/default.png';

  static bool listHasMore(int count, int page, int size) {
    return count - page * size > 0;
  }

  static DeviceType? deviceType;

  /// Although specifying the `BuildContext` is optional, providing it can prevent layout issues when this widget renders immediately after the app launch.
  /// If this widget needs to be used at the moment the app launches, it's recommended to provide the `BuildContext` here.
  static DeviceType getFormFactor([BuildContext? context]) {
    if (deviceType != null) return deviceType!;

    if (PlatformUtils().isWeb) {
      final win = WidgetsBinding.instance.platformDispatcher.views.first;
      final size = win.physicalSize;
      final screenWidth = size.width / win.devicePixelRatio;
      final screenHeight = size.height / win.devicePixelRatio;

      final diagonalInInches =
          sqrt(pow(screenWidth, 2) + pow(screenHeight, 2)) / 96.0;

      deviceType =
          diagonalInInches < 11.0 ? DeviceType.Mobile : DeviceType.Desktop;
      return deviceType ?? DeviceType.Mobile;
    } else {
      if (context != null) {
        double deviceWidth = MediaQuery.of(context).size.width;
        double deviceHeight = MediaQuery.of(context).size.height;

        if (deviceWidth > FormFactor.desktop ||
            deviceWidth > deviceHeight * 1.1) {
          deviceType = DeviceType.Desktop;
        } else if (deviceWidth > FormFactor.handset) {
          deviceType = DeviceType.Mobile;
        }
        return deviceType ?? DeviceType.Mobile;
      } else {
        return DeviceType.Mobile;
      }
    }
  }

  static Widget getDeviceWidget({
    /// Although specifying the `BuildContext` is optional, providing it can prevent layout issues when this widget renders immediately after the app launch.
    /// If this widget needs to be used at the moment the app launches, it's recommended to provide the `BuildContext` here.
    BuildContext? context,
    required Widget defaultWidget,
    Widget? desktopWidget,
    Widget? mobileWidget,
  }) {
    deviceType ??= getFormFactor(context);
    if (deviceType == DeviceType.Desktop) return desktopWidget ?? defaultWidget;
    return mobileWidget ?? defaultWidget;
  }

  static String delHL(String? content) {
    content = content ?? '';
    return content.replaceAll('<hl>', '').replaceAll('</hl>', '');
  }

  static Future<void> writeErrorLog(String log) async {
    try {
      final directory = Platform.isIOS
          ? await getApplicationDocumentsDirectory()
          : await getExternalStorageDirectory();
      final file = File('${directory?.path}/npemployee_error_log.txt');
      final now = DateTime.now().toString();
      await file.writeAsString('[$now] $log\n', mode: FileMode.append);
    } catch (e) {
      debugPrint('写入日志失败');
    }
  }

  static Future<String> getNetworkStatusLog() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    String type = '未知';
    if (connectivityResult.contains(ConnectivityResult.wifi)) {
      type = 'Wi-Fi';
    } else if (connectivityResult.contains(ConnectivityResult.mobile)) {
      type = '手机网络';
    } else if (connectivityResult.contains(ConnectivityResult.ethernet)) {
      type = '有线网络';
    } else if (connectivityResult.contains(ConnectivityResult.none)) {
      type = '无网络';
    } else {
      type = connectivityResult.toString();
    }
    return type;
  }
}

class PlatformUtils {
  PlatformUtils._internal();
  static late bool _isAndroid;
  static late bool _isIos;
  static late bool _isMobile;
  static late bool _isWeb;
  static late bool _isWindows;
  static late bool _isMacOS;
  static late bool _isLinux;
  static late bool _isDesktop;
  static bool _isInstantiation = false;

  factory PlatformUtils() {
    if (!_isInstantiation) {
      _isAndroid = !kIsWeb && Platform.isAndroid;
      _isIos = !kIsWeb && Platform.isIOS;
      _isMobile = _isAndroid || _isIos;
      _isWindows = !kIsWeb && Platform.isWindows;
      _isMacOS = !kIsWeb && Platform.isMacOS;
      _isLinux = !kIsWeb && Platform.isLinux;
      _isDesktop = _isMacOS || _isWindows || _isLinux;
      _isWeb = kIsWeb;
      _isInstantiation = true;
    }

    return _instance;
  }

  static late final PlatformUtils _instance = PlatformUtils._internal();

  get isAndroid {
    return _isAndroid;
  }

  get isWeb {
    return _isWeb;
  }

  get isIOS {
    return _isIos;
  }

  get isWindows {
    return _isWindows;
  }

  get isMacOS {
    return _isMacOS;
  }

  get isMobile {
    return _isMobile;
  }

  bool get isDesktop => _isDesktop;

  bool get isLinux => _isLinux;
}
