import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/manager/local_cache_manager.dart';
import 'package:npemployee/page/login/login_event.dart';
import 'package:package_info/package_info.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tencent_calls_uikit/tuicall_kit.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
import '../constants/GlobalPreferences.dart';

class AppInfo {
  bool? _isLogin = false;

  // static bool isProduction = const bool.fromEnvironment("dart.vm.product");
  static bool isProduction = true;

  get isDebug => !isProduction;

  static AppInfo? instance;

  static AppInfo? getInstance() {
    instance ??= AppInfo();
    return instance;
  }

  get tokenKey async {
    return "Authorization";
  }

  Future<String?> get tokenValue async {
    String? token = GlobalPreferences().tokenValue;
    return "Bearer $token";
  }

  get imId => isProduction ? ********** : **********;

  get imApnsId => isProduction ? 44463 : 44355;

  get umAndroidId => '678da7049a16fe6dcd324c8a';
  get umIosID => '678da8079a16fe6dcd324de0';
  get umChannel => 'Flutter';

  get friendCircleUrl =>
      "https://m.xtjzx.cn/app/friend_circle_box/#/index?source=app";

  get growthChartUrl => "https://m.xtjzx.cn/dev/xtj-people-h5";

  // 添加一个新的字段来存储最新版本
  String? _latestVersion;

// 提供一个公共的getter来访问最新版本
  String? get latestVersion => _latestVersion;

// 提供一个公共的setter来修改最新版本
  set latestVersion(String? value) {
    _latestVersion = value;
  }

  String? _membershipType;
  String? get membershipType => _membershipType;
  set membershipType(String? value) {
    _membershipType = value;
  }

  // 计算属性，判断用户是否为会员
  bool get isMember {
    return _membershipType != null &&
        _membershipType != "non-member" &&
        _membershipType!.isNotEmpty;
  }

  String? _vipExpiration;

  String? get vipExpiration => _vipExpiration;

  set vipExpiration(String? value) {
    _vipExpiration = value;
  }

  set setIsLogin(bool? value) {
    this._isLogin = value;
  }

  bool? get isLogin {
    return _isLogin;
  }

  bool get hasLogined {
    // debugPrint('------ token = ${GlobalPreferences().tokenValue}');
    return GlobalPreferences().tokenValue != null &&
        GlobalPreferences().tokenValue!.isNotEmpty;
  }

  bool get registered {
    // debugPrint('------ registered = ${GlobalPreferences().userInfo}');
    return GlobalPreferences().userInfo != null ||
        GlobalPreferences().oldUserInfo != null ||
        GlobalPreferences().guestInfo != null ||
        GlobalPreferences().userSubInfo != null;
  }

  ///获取APP版本号
  Future<String> getFlutterAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  /// 公共日志
  void appLog(String? content, {String name = ''}) {
    if (isDebug) {
      log(content!, name: name);
    }
  }

  void clearLoginStatu() {
    try {
      UmengCommonSdk.onProfileSignOff();
      TIMUIKitCore.getInstance().logout();
      TIMUIKitCore.getSDKInstance().logout();
      TUICallKit.instance.logout();

      GlobalPreferences().tokenValue = null;
      GlobalPreferences().userInfo = null;
      GlobalPreferences().guestInfo = null;
      GlobalPreferences().oldUserInfo = null;
      GlobalPreferences().userSubInfo = null;
      GlobalPreferences().imLoginModel = null;

      LocalCacheManager.shared.clearAll();
      BlocManager().loginBloc.add(LoginSuccessEvent(false));
    } catch (e) {
      debugPrint('-------------------- 登出error = ${e.toString()}');
    }
  }

  Future<String?> _getSavedDir() async {
    String? externalStorageDirPath;
    if (Platform.isIOS) {
      externalStorageDirPath =
          (await getApplicationDocumentsDirectory()).absolute.path;
    } else if (Platform.isAndroid) {
      externalStorageDirPath =
          (await getExternalStorageDirectories())?.first.absolute.path ?? '';
    }

    return externalStorageDirPath;
  }

  Future<String> get handoutDownloadDir async {
    String path = await _getSavedDir() ?? '';
    String dirPath = '$path/handout_downloads';
    final savedDir = Directory(dirPath);
    if (!savedDir.existsSync()) {
      await savedDir.create();
    }
    return dirPath;
  }

  Future<String> get capabilityDownloadDir async {
    String path = await _getSavedDir() ?? '';
    String dirPath = '$path/capability_downloads';
    final savedDir = Directory(dirPath);
    if (!savedDir.existsSync()) {
      await savedDir.create();
    }
    return dirPath;
  }

  Future<String> get pptDownloadDir async {
    String path = await _getSavedDir() ?? '';
    String dirPath = '$path/meeting_ppt';
    final savedDir = Directory(dirPath);
    if (!savedDir.existsSync()) {
      await savedDir.create();
    }
    return dirPath;
  }

  Future<String> get upgradeDownloadDir async {
    String path = await _getSavedDir() ?? '';
    String dirPath = '$path/upgrade_downloads';
    final savedDir = Directory(dirPath);
    if (!savedDir.existsSync()) {
      await savedDir.create();
    }
    return dirPath;
  }
}
