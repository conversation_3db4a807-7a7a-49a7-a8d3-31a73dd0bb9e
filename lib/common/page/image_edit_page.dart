import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/provider/avatar_bloc/avatar_event.dart';
import 'package:npemployee/provider/tab_bloc/tab_event.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class ImageEditPage extends StatefulWidget {
  final String imagePath;

  const ImageEditPage({Key? key, required this.imagePath}) : super(key: key);

  @override
  _ImageEditPageState createState() => _ImageEditPageState();
}

class _ImageEditPageState extends State<ImageEditPage> {
  @override
  void initState() {
    super.initState();
    _cropImage();
  }

  Future<void> _cropImage() async {
    final croppedFile = await ImageCropper().cropImage(
      sourcePath: widget.imagePath,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: '编辑头像',
          toolbarColor: Colors.black,
          toolbarWidgetColor: Colors.white,
          // initAspectRatio: CropAspectRatioPreset.square,
          // lockAspectRatio: true,
        ),
        IOSUiSettings(
          title: '编辑头像',
          aspectRatioLockEnabled: true,
        ),
      ],
    );
    debugPrint('-------------------- 图片文件路径 = ${croppedFile?.path}');
    if (croppedFile != null) {
      EasyLoading.show();
      UserServiceProvider().uploadAvatar(croppedFile.path).then((value) {
        if (value?.code == 0) {
          UserServiceProvider().setAvatar(value?.data).then((value) {
            if (value?.code == 0) {
              EasyLoading.dismiss();
              BlocManager().tabBloc.add(TabChangeEvent(4));
              BlocManager().avatarBloc.add(AvatarChangeEvent(croppedFile.path));
              NavigatorUtils.popUntil(context, MineRouter.personalSettingsPage);
            } else {
              EasyLoading.dismiss();
              EasyLoading.showError(value?.msg ?? '上传失败');
            }
          });
        } else {
          EasyLoading.dismiss();
          EasyLoading.showError(value?.msg ?? '上传失败');
        }
      });
    } else {
      NavigatorUtils.popUntil(context, MineRouter.personalSettingsPage);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Image.file(File(widget.imagePath)),
      ),
    );
  }
}
